    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <!-- Sidebar Search Input -->
        <div class="sidebar-search">
            <div class="mdc-text-field mdc-text-field--filled mdc-text-field--no-label mdc-text-field--with-leading-icon">
                <span class="mdc-text-field__icon mdc-text-field__icon--leading material-icons">search</span>
                <input class="mdc-text-field__input"
                       type="text"
                       placeholder="Rechercher dans le menu"
                       id="sidebar-search-input">
            </div>
        </div>

        <!-- Section 1: Dashboard & Student Management -->
        <div class="sidebar-section">
            <div class="sidebar-section-title">VIE SCOLAIRE</div>
            <a href="#" class="sidebar-item" @click="setActiveNav('home');" :class="{ 'active' : activeNav === 'home'}"
                hx-get="{% url 'school:home' %}" hx-target="#content-area" hx-push-url="{% url 'school:home' %}">
                <span class="material-icons">dashboard</span>
                <span>Accueil</span>
            </a>
            <a href="#" class="sidebar-item sidebar-item-with-submenu"
            :class="{ 'active' : activeNav === 'eleves' }"
            data-submenu="eleves-submenu">
            <span class="material-icons">groups_2</span>
            <span>Élèves</span>
            <span class="material-icons expand-icon">expand_more</span>
            </a>
            <div class="submenu" id="eleves-submenu">
            <a href="#" class="submenu-item" @click.prevent="setActiveNav('manage-eleves'); pageTitle = 'Gestion des élèves'"
               hx-get="{% url 'school:students' %}" hx-target="#main-content"
               hx-push-url="{% url 'school:students' %}">
                <span class="material-icons submenu-icon">group_add</span>
                <span>Gestion des élèves</span>
            </a>
            <a href="#" class="submenu-item" @click.prevent="setActiveNav('eleves-cards')">
                <span class="material-icons submenu-icon">format_list_numbered</span>
                <span>Attribution des classes</span>
            </a>
            <a href="#" class="submenu-item" @click.prevent="setActiveNav('eleves-cards')">
                <span class="material-icons submenu-icon">photo_camera</span>
                <span>Gestion des photos</span>
            </a>
            <a href="#" class="submenu-item" @click.prevent="setActiveNav('eleves-cards')">
                <span class="material-icons submenu-icon">event_busy</span>
                <span>Gestion des absences</span>
            </a>
            </div>
            <div class="sidebar-item sidebar-item-with-submenu" data-submenu="section2-submenu" :class="{ 'active' : activeNav === 'section2'}">
                <span class="material-icons">leaderboard</span>
                <span>Niveaux et Classes</span>
                <span class="material-icons expand-icon">expand_more</span>
            </div>
            <div class="submenu" id="section2-submenu">
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection4')">
                    <span class="material-icons submenu-icon">stairs</span>
                    <span>Niveaux</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection4')">
                    <span class="material-icons submenu-icon">class</span>
                    <span>Classes</span>
                </a>
            </div>
            <div class="sidebar-item sidebar-item-with-submenu" :class="{ 'active' : activeNav === 'section1'}" data-submenu="section1-submenu">
                <span class="material-icons">request_quote</span>
                <span>Comptabilité</span>
                <span class="material-icons expand-icon">expand_more</span>
            </div>
            <div class="submenu" id="section1-submenu">
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection1')">
                    <span class="material-icons submenu-icon">history</span>
                    <span>Historique des paiements</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection2')">
                    <span class="material-icons submenu-icon">assessment</span>
                    <span>Rapport des paiements</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection2')">
                    <span class="material-icons submenu-icon">account_circle</span>
                    <span>Gestion des comptables</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection2')">
                    <span class="material-icons submenu-icon">school</span>
                    <span>Frais de scolarité</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection2')">
                    <span class="material-icons submenu-icon">account_balance_wallet</span>
                    <span>Solde et dépenses</span>
                </a>
            </div>
            <div class="sidebar-item sidebar-item-with-submenu" data-submenu="section3-submenu" :class="{ 'active' : activeNav === 'section3'}">
                <span class="material-icons">calculate</span>
                <span>Notes et Moyennes</span>
                <span class="material-icons expand-icon">expand_more</span>
            </div>
            <div class="submenu" id="section3-submenu">
                <div class="submenu-item submenu-item-with-submenu" data-submenu="subsection3-submenu">
                    <span class="material-icons submenu-icon">translate</span>
                    <span>Français</span>
                    <span class="material-icons expand-icon">expand_more</span>
                </div>
                <div class="submenu submenu-level-2" id="subsection3-submenu">
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie1')">Fiches de Notation</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Notes</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Moyennes</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Résultats</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Bulletins</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Décisions Annuelles (DFA)</a>
                </div>

                <div class="submenu-item submenu-item-with-submenu" data-submenu="subsection4-submenu">
                    <span class="material-icons submenu-icon">language</span>
                    <span>Arabe</span>
                    <span class="material-icons expand-icon">expand_more</span>
                </div>
                <div class="submenu submenu-level-2" id="subsection4-submenu">
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie1')">Fiches de Notation</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Notes</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Moyennes</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Résultats</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Bulletins</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Décisions Annuelles (DFA)</a>
                </div>
            </div>
        </div>

        <!-- Section 2: Administration & Management -->
        <div class="sidebar-section">
            <div class="sidebar-section-title">ADMINISTRATION</div>
            <div class="sidebar-item sidebar-item-with-submenu" data-submenu="docs-submenu">
                <span class="material-icons">description</span>
                <span>Documents Administratifs</span>
                <span class="material-icons expand-icon">expand_more</span>
            </div>
            <div class="submenu" id="docs-submenu">
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">
                    <span class="material-icons submenu-icon">list_alt</span>
                    <span>Listes de classe</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">
                    <span class="material-icons submenu-icon">calendar_month</span>
                    <span>Registres d'Appel</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">
                    <span class="material-icons submenu-icon">folder_open</span>
                    <span>Fiches Scolaires</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie1')">
                    <span class="material-icons submenu-icon">badge</span>
                    <span>Cartes Scolaires</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie1')">
                    <span class="material-icons submenu-icon">more_horiz</span>
                    <span>Divers</span>
                </a>
            </div>
            <div class="sidebar-item sidebar-item-with-submenu" data-submenu="section4-submenu" :class="{ 'active' : activeNav === 'section4'}">
                <span class="material-icons">home</span>
                <span>Ecole</span>
                <span class="material-icons expand-icon">expand_more</span>
            </div>
            <div class="submenu" id="section4-submenu">
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection4')">
                    <span class="material-icons submenu-icon">info</span>
                    <span>Infos Ecole</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection4')">
                    <span class="material-icons submenu-icon">business</span>
                    <span>Batiments et Salles</span>
                </a>
            </div>
            <div class="sidebar-item sidebar-item-with-submenu" data-submenu="section8-submenu" :class="{ 'active' : activeNav === 'section8'}">
                <span class="material-icons">school</span>
                <span>Pédagogie</span>
                <span class="material-icons expand-icon">expand_more</span>
            </div>
            <div class="submenu" id="section8-submenu">
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection8')">
                    <span class="material-icons submenu-icon">date_range</span>
                    <span>Périodicité • Trimestres</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection8')">
                    <span class="material-icons submenu-icon">subject</span>
                    <span>Matières</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection8')">
                    <span class="material-icons submenu-icon">grade</span>
                    <span>Mentions</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection8')">
                    <span class="material-icons submenu-icon">schedule</span>
                    <span>Emploi du temps</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection8')">
                    <span class="material-icons submenu-icon">assignment</span>
                    <span>Devoirs</span>
                </a>
            </div>
            <div class="sidebar-item sidebar-item-with-submenu" data-submenu="section7-submenu" :class="{ 'active' : activeNav === 'section7'}">
                <span class="material-icons">people</span>
                <span>Ressources Humaines</span>
                <span class="material-icons expand-icon">expand_more</span>
            </div>
            <div class="submenu" id="section7-submenu">
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection7')">
                    <span class="material-icons submenu-icon">person_add</span>
                    <span>Enseignants</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection7')">
                    <span class="material-icons submenu-icon">groups_3</span>
                    <span>Administration</span>
                </a>
                <!--  Gestion des salaires -->
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection7')">
                    <span class="material-icons submenu-icon">paid</span>
                    <span>Gestion des salaires</span>
                </a>
            </div>
        </div>

        <!-- Section 3: Tools & Communication -->
        <div class="sidebar-section">
            <div class="sidebar-section-title">DIVERS</div>
            <a href="#" class="sidebar-item" @click="setActiveNav('messaging')" :class="{ 'active' : activeNav === 'messaging'}">
                <span class="material-icons">mail</span>
                <span>Messagerie</span>
            </a>
            <a href="#" class="sidebar-item" @click="setActiveNav('wallet')" :class="{ 'active' : activeNav === 'wallet'}">
                <span class="material-icons">subscriptions</span>
                <span>Formation EcolePro</span>
            </a>
            <a href="#" class="sidebar-item" @click="setActiveNav('parnetship')" :class="{ 'active' : activeNav === 'parnetship'}">
                <span class="material-icons">diversity_3</span>
                <span>Devenir partenaire</span>
            </a>
        </div>
    </div>