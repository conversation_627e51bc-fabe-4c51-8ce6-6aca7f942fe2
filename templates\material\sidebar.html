    <!-- Sidebar -->
    <div class="sidebar" id="sidebar" x-data="dynamicSidebarNavigation()" :class="{ 'sidebar-search-active': searchActive }">
        <!-- Sidebar Search Input -->
        <div class="sidebar-search">
            <div class="mdc-text-field mdc-text-field--filled mdc-text-field--no-label mdc-text-field--with-leading-icon">
                <span class="mdc-text-field__icon mdc-text-field__icon--leading material-icons">search</span>
                <input class="mdc-text-field__input"
                       type="text"
                       placeholder="Rechercher dans le menu"
                       id="sidebar-search-input"
                       x-model="searchQuery"
                       @input="performSearch()"
                       @focus="searchActive = true"
                       @blur="if (!searchQuery) searchActive = false">
            </div>
        </div>

        <!-- Search Results -->
        <div class="sidebar-search-results">
            <template x-for="result in searchResults" :key="result.id">
                <a href="#" class="search-result-item" @click="navigateToItem(result)">
                    <span class="material-icons" x-text="result.icon"></span>
                    <div class="search-result-content">
                        <div class="search-result-title" x-text="result.title"></div>
                        <div class="search-result-path" x-text="result.path"></div>
                    </div>
                </a>
            </template>
            <div x-show="searchQuery && searchResults.length === 0" class="search-no-results" style="padding: 20px; text-align: center; color: var(--text-muted);">
                Aucun résultat trouvé
            </div>
        </div>

        <!-- Drill-down Container -->
        <div class="sidebar-drill-down">
            <!-- Main Sidebar View -->
            <div class="sidebar-main-view" :class="{ 'slide-out': showSubmenuDetail }">

                <!-- Section 1: Dashboard & Student Management -->
                <div class="sidebar-section">
                    <div class="sidebar-section-title">VIE SCOLAIRE</div>
                    <a href="#" class="sidebar-item" @click="setActiveNav('home');" :class="{ 'active' : activeNav === 'home'}"
                        hx-get="{% url 'school:home' %}" hx-target="#main-content" hx-push-url="{% url 'school:home' %}">
                        <span class="material-icons">dashboard</span>
                        <span>Accueil</span>
                    </a>
                    <a href="#" class="sidebar-item sidebar-item-with-submenu"
                    :class="{ 'active' : activeNav === 'eleves' }"
                    data-submenu-id="eleves"
                    data-submenu-title="Élèves"
                    @click.prevent="openSubmenu($event.currentTarget)">
                    <span class="material-icons">groups_2</span>
                    <span>Élèves</span>
                    <span class="material-icons expand-icon">chevron_right</span>
                    </a>
                    <div class="submenu" id="eleves-submenu" data-submenu-section="Gestion des Élèves">
                        <a href="#" class="submenu-item"
                           data-nav="manage-eleves"
                           data-title="Gestion des élèves"
                           hx-get="{% url 'school:students' %}" hx-target="#main-content"
                           hx-push-url="{% url 'school:students' %}">
                            <span class="material-icons submenu-icon">group_add</span>
                            <span>Gestion des élèves</span>
                            <span class="description">Ajouter, modifier et gérer les élèves</span>
                        </a>
                        <a href="#" class="submenu-item"
                           data-nav="eleves-attribution"
                           data-title="Attribution des classes">
                            <span class="material-icons submenu-icon">format_list_numbered</span>
                            <span>Attribution des classes</span>
                            <span class="description">Assigner les élèves aux classes</span>
                        </a>
                        <a href="#" class="submenu-item"
                           data-nav="eleves-photos"
                           data-title="Gestion des photos">
                            <span class="material-icons submenu-icon">photo_camera</span>
                            <span>Gestion des photos</span>
                            <span class="description">Importer et gérer les photos</span>
                        </a>
                        <a href="#" class="submenu-item"
                           data-nav="eleves-absences"
                           data-title="Gestion des absences">
                            <span class="material-icons submenu-icon">event_busy</span>
                            <span>Gestion des absences</span>
                            <span class="description">Suivre les absences et retards</span>
                        </a>
                    </div>
                    <a href="#" class="sidebar-item sidebar-item-with-submenu"
                       :class="{ 'active' : activeNav === 'section2'}"
                       data-submenu-id="niveaux"
                       data-submenu-title="Niveaux et Classes"
                       @click.prevent="openSubmenu($event.currentTarget)">
                        <span class="material-icons">leaderboard</span>
                        <span>Niveaux et Classes</span>
                        <span class="material-icons expand-icon">chevron_right</span>
                    </a>
                    <div class="submenu" id="niveaux-submenu" data-submenu-section="Organisation Scolaire">
                        <a href="#" class="submenu-item"
                           data-nav="subsection4"
                           data-title="Niveaux">
                            <span class="material-icons submenu-icon">stairs</span>
                            <span>Niveaux</span>
                            <span class="description">Gérer les niveaux d'enseignement</span>
                        </a>
                        <a href="#" class="submenu-item"
                           data-nav="subsection4"
                           data-title="Classes">
                            <span class="material-icons submenu-icon">class</span>
                            <span>Classes</span>
                            <span class="description">Organiser les classes par niveau</span>
                        </a>
                    </div>
                    <a href="#" class="sidebar-item sidebar-item-with-submenu"
                       :class="{ 'active' : activeNav === 'section1'}"
                       data-submenu-id="section1"
                       data-submenu-title="Comptabilité"
                       @click.prevent="openSubmenu($event.currentTarget)">
                        <span class="material-icons">request_quote</span>
                        <span>Comptabilité</span>
                        <span class="material-icons expand-icon">chevron_right</span>
                    </a>
                    <div class="submenu" id="section1-submenu" data-submenu-section="Gestion Financière">
                        <a href="#" class="submenu-item"
                           data-nav="subsection1"
                           data-title="Historique des paiements">
                            <span class="material-icons submenu-icon">history</span>
                            <span>Historique des paiements</span>
                            <span class="description">Consulter l'historique des paiements</span>
                        </a>
                        <a href="#" class="submenu-item"
                           data-nav="subsection2"
                           data-title="Rapport des paiements">
                            <span class="material-icons submenu-icon">assessment</span>
                            <span>Rapport des paiements</span>
                            <span class="description">Générer des rapports financiers</span>
                        </a>
                        <a href="#" class="submenu-item"
                           data-nav="subsection2"
                           data-title="Gestion des comptables">
                            <span class="material-icons submenu-icon">account_circle</span>
                            <span>Gestion des comptables</span>
                            <span class="description">Gérer les comptes utilisateurs</span>
                        </a>
                        <a href="#" class="submenu-item"
                           data-nav="subsection2"
                           data-title="Frais de scolarité">
                            <span class="material-icons submenu-icon">school</span>
                            <span>Frais de scolarité</span>
                            <span class="description">Configurer les frais par niveau</span>
                        </a>
                        <a href="#" class="submenu-item"
                           data-nav="subsection2"
                           data-title="Solde et dépenses">
                            <span class="material-icons submenu-icon">account_balance_wallet</span>
                            <span>Solde et dépenses</span>
                            <span class="description">Gérer le budget et les dépenses</span>
                        </a>
                    </div>
                    <a href="#" class="sidebar-item sidebar-item-with-submenu"
                       :class="{ 'active' : activeNav === 'section3'}"
                       data-submenu-id="section3"
                       data-submenu-title="Notes et Moyennes"
                       @click.prevent="openSubmenu($event.currentTarget)">
                        <span class="material-icons">calculate</span>
                        <span>Notes et Moyennes</span>
                        <span class="material-icons expand-icon">chevron_right</span>
                    </a>
                    <div class="submenu" id="section3-submenu" data-submenu-section="Évaluation Scolaire">
                        <!-- Français Level 2 Accordion -->
                        <a href="#" class="submenu-item"
                           data-level2-id="francais"
                           data-nav="francais-section"
                           data-title="Français">
                            <span class="material-icons submenu-icon">translate</span>
                            <span>Français</span>
                            <span class="description">Notes et évaluations en français</span>
                            <span class="material-icons expand-icon">expand_more</span>
                        </a>

                        <!-- Hidden Level 2 Items for Français -->
                        <div id="francais-level2" style="display: none;">
                            <a href="#" class="submenu-item"
                               data-nav="souspartie1"
                               data-title="Fiches de Notation - Français">
                                <span class="material-icons submenu-icon">assignment</span>
                                <span>Fiches de Notation</span>
                                <span class="description">Créer et gérer les fiches</span>
                            </a>
                            <a href="#" class="submenu-item"
                               data-nav="souspartie2"
                               data-title="Notes - Français">
                                <span class="material-icons submenu-icon">grade</span>
                                <span>Notes</span>
                                <span class="description">Saisir et modifier les notes</span>
                            </a>
                            <a href="#" class="submenu-item"
                               data-nav="souspartie2"
                               data-title="Moyennes - Français">
                                <span class="material-icons submenu-icon">calculate</span>
                                <span>Moyennes</span>
                                <span class="description">Calculer les moyennes</span>
                            </a>
                            <a href="#" class="submenu-item"
                               data-nav="souspartie2"
                               data-title="Résultats - Français">
                                <span class="material-icons submenu-icon">analytics</span>
                                <span>Résultats</span>
                                <span class="description">Consulter les résultats</span>
                            </a>
                            <a href="#" class="submenu-item"
                               data-nav="souspartie2"
                               data-title="Bulletins - Français">
                                <span class="material-icons submenu-icon">description</span>
                                <span>Bulletins</span>
                                <span class="description">Générer les bulletins</span>
                            </a>
                            <a href="#" class="submenu-item"
                               data-nav="souspartie2"
                               data-title="Décisions Annuelles (DFA) - Français">
                                <span class="material-icons submenu-icon">gavel</span>
                                <span>Décisions Annuelles (DFA)</span>
                                <span class="description">Gérer les décisions finales</span>
                            </a>
                        </div>

                        <!-- Arabe Level 2 Accordion -->
                        <a href="#" class="submenu-item"
                           data-level2-id="arabe"
                           data-nav="arabe-section"
                           data-title="Arabe">
                            <span class="material-icons submenu-icon">language</span>
                            <span>Arabe</span>
                            <span class="description">Notes et évaluations en arabe</span>
                            <span class="material-icons expand-icon">expand_more</span>
                        </a>

                        <!-- Hidden Level 2 Items for Arabe -->
                        <div id="arabe-level2" style="display: none;">
                            <a href="#" class="submenu-item"
                               data-nav="souspartie1"
                               data-title="Fiches de Notation - Arabe">
                                <span class="material-icons submenu-icon">assignment</span>
                                <span>Fiches de Notation</span>
                                <span class="description">Créer et gérer les fiches</span>
                            </a>
                            <a href="#" class="submenu-item"
                               data-nav="souspartie2"
                               data-title="Notes - Arabe">
                                <span class="material-icons submenu-icon">grade</span>
                                <span>Notes</span>
                                <span class="description">Saisir et modifier les notes</span>
                            </a>
                            <a href="#" class="submenu-item"
                               data-nav="souspartie2"
                               data-title="Moyennes - Arabe">
                                <span class="material-icons submenu-icon">calculate</span>
                                <span>Moyennes</span>
                                <span class="description">Calculer les moyennes</span>
                            </a>
                            <a href="#" class="submenu-item"
                               data-nav="souspartie2"
                               data-title="Résultats - Arabe">
                                <span class="material-icons submenu-icon">analytics</span>
                                <span>Résultats</span>
                                <span class="description">Consulter les résultats</span>
                            </a>
                            <a href="#" class="submenu-item"
                               data-nav="souspartie2"
                               data-title="Bulletins - Arabe">
                                <span class="material-icons submenu-icon">description</span>
                                <span>Bulletins</span>
                                <span class="description">Générer les bulletins</span>
                            </a>
                            <a href="#" class="submenu-item"
                               data-nav="souspartie2"
                               data-title="Décisions Annuelles (DFA) - Arabe">
                                <span class="material-icons submenu-icon">gavel</span>
                                <span>Décisions Annuelles (DFA)</span>
                                <span class="description">Gérer les décisions finales</span>
                            </a>
                        </div>

                        <!-- General Notes Item -->
                        <a href="#" class="submenu-item"
                           data-nav="notes-generales"
                           data-title="Notes Générales">
                            <span class="material-icons submenu-icon">assignment</span>
                            <span>Notes Générales</span>
                            <span class="description">Vue d'ensemble des notes</span>
                        </a>
                    </div>
        </div>

        <!-- Section 2: Administration & Management -->
        <div class="sidebar-section">
            <div class="sidebar-section-title">ADMINISTRATION</div>
                    <a href="#" class="sidebar-item sidebar-item-with-submenu"
                       data-submenu-id="docs"
                       data-submenu-title="Documents Administratifs"
                       @click.prevent="openSubmenu($event.currentTarget)">
                        <span class="material-icons">description</span>
                        <span>Documents Administratifs</span>
                        <span class="material-icons expand-icon">chevron_right</span>
                    </a>
                    <div class="submenu" id="docs-submenu" data-submenu-section="Documents Administratifs">
                        <a href="#" class="submenu-item"
                           data-nav="souspartie2"
                           data-title="Listes de classe">
                            <span class="material-icons submenu-icon">list_alt</span>
                            <span>Listes de classe</span>
                            <span class="description">Générer les listes d'élèves</span>
                        </a>
                        <a href="#" class="submenu-item"
                           data-nav="souspartie2"
                           data-title="Registres d'Appel">
                            <span class="material-icons submenu-icon">calendar_month</span>
                            <span>Registres d'Appel</span>
                            <span class="description">Gérer les registres d'appel</span>
                        </a>
                        <a href="#" class="submenu-item"
                           data-nav="souspartie2"
                           data-title="Fiches Scolaires">
                            <span class="material-icons submenu-icon">folder_open</span>
                            <span>Fiches Scolaires</span>
                            <span class="description">Gérer les fiches scolaires</span>
                        </a>
                        <a href="#" class="submenu-item"
                           data-nav="souspartie1"
                           data-title="Cartes Scolaires">
                            <span class="material-icons submenu-icon">badge</span>
                            <span>Cartes Scolaires</span>
                            <span class="description">Générer les cartes d'élèves</span>
                        </a>
                        <a href="#" class="submenu-item"
                           data-nav="souspartie1"
                           data-title="Divers">
                            <span class="material-icons submenu-icon">more_horiz</span>
                            <span>Divers</span>
                            <span class="description">Autres documents</span>
                        </a>
                    </div>
                    <a href="#" class="sidebar-item sidebar-item-with-submenu"
                       :class="{ 'active' : activeNav === 'section4'}"
                       data-submenu-id="section4"
                       data-submenu-title="École"
                       @click.prevent="openSubmenu($event.currentTarget)">
                        <span class="material-icons">home</span>
                        <span>École</span>
                        <span class="material-icons expand-icon">chevron_right</span>
                    </a>
                    <div class="submenu" id="section4-submenu" data-submenu-section="École">
                        <a href="#" class="submenu-item"
                           data-nav="subsection4"
                           data-title="Infos École">
                            <span class="material-icons submenu-icon">info</span>
                            <span>Infos École</span>
                            <span class="description">Informations générales</span>
                        </a>
                        <a href="#" class="submenu-item"
                           data-nav="subsection4"
                           data-title="Bâtiments et Salles">
                            <span class="material-icons submenu-icon">business</span>
                            <span>Bâtiments et Salles</span>
                            <span class="description">Gérer les infrastructures</span>
                        </a>
                    </div>
                    <a href="#" class="sidebar-item sidebar-item-with-submenu"
                       :class="{ 'active' : activeNav === 'section8'}"
                       data-submenu-id="section8"
                       data-submenu-title="Pédagogie"
                       @click.prevent="openSubmenu($event.currentTarget)">
                        <span class="material-icons">school</span>
                        <span>Pédagogie</span>
                        <span class="material-icons expand-icon">chevron_right</span>
                    </a>
                    <div class="submenu" id="section8-submenu" data-submenu-section="Pédagogie">
                        <a href="#" class="submenu-item"
                           data-nav="subsection8"
                           data-title="Périodicité • Trimestres">
                            <span class="material-icons submenu-icon">date_range</span>
                            <span>Périodicité • Trimestres</span>
                            <span class="description">Configurer les périodes</span>
                        </a>
                        <a href="#" class="submenu-item"
                           data-nav="subsection8"
                           data-title="Matières">
                            <span class="material-icons submenu-icon">subject</span>
                            <span>Matières</span>
                            <span class="description">Gérer les matières enseignées</span>
                        </a>
                        <a href="#" class="submenu-item"
                           data-nav="subsection8"
                           data-title="Mentions">
                            <span class="material-icons submenu-icon">grade</span>
                            <span>Mentions</span>
                            <span class="description">Configurer les mentions</span>
                        </a>
                        <a href="#" class="submenu-item"
                           data-nav="subsection8"
                           data-title="Emploi du temps">
                            <span class="material-icons submenu-icon">schedule</span>
                            <span>Emploi du temps</span>
                            <span class="description">Gérer les horaires</span>
                        </a>
                        <a href="#" class="submenu-item"
                           data-nav="subsection8"
                           data-title="Devoirs">
                            <span class="material-icons submenu-icon">assignment</span>
                            <span>Devoirs</span>
                            <span class="description">Gérer les devoirs</span>
                        </a>
                    </div>
                    <a href="#" class="sidebar-item sidebar-item-with-submenu"
                       :class="{ 'active' : activeNav === 'section7'}"
                       data-submenu-id="section7"
                       data-submenu-title="Ressources Humaines"
                       @click.prevent="openSubmenu($event.currentTarget)">
                        <span class="material-icons">people</span>
                        <span>Ressources Humaines</span>
                        <span class="material-icons expand-icon">chevron_right</span>
                    </a>
                    <div class="submenu" id="section7-submenu" data-submenu-section="Ressources Humaines">
                        <a href="#" class="submenu-item"
                           data-nav="subsection7"
                           data-title="Enseignants">
                            <span class="material-icons submenu-icon">person_add</span>
                            <span>Enseignants</span>
                            <span class="description">Gérer les enseignants</span>
                        </a>
                        <a href="#" class="submenu-item"
                           data-nav="subsection7"
                           data-title="Administration">
                            <span class="material-icons submenu-icon">groups_3</span>
                            <span>Administration</span>
                            <span class="description">Personnel administratif</span>
                        </a>
                        <a href="#" class="submenu-item"
                           data-nav="subsection7"
                           data-title="Gestion des salaires">
                            <span class="material-icons submenu-icon">paid</span>
                            <span>Gestion des salaires</span>
                            <span class="description">Gérer les salaires</span>
                        </a>
                    </div>
        </div>

        <!-- Section 3: Tools & Communication -->
        <div class="sidebar-section">
            <div class="sidebar-section-title">DIVERS</div>
            <a href="#" class="sidebar-item" @click="setActiveNav('messaging')" :class="{ 'active' : activeNav === 'messaging'}">
                <span class="material-icons">mail</span>
                <span>Messagerie</span>
            </a>
            <a href="#" class="sidebar-item" @click="setActiveNav('wallet')" :class="{ 'active' : activeNav === 'wallet'}">
                <span class="material-icons">subscriptions</span>
                <span>Formation EcolePro</span>
            </a>
            <a href="#" class="sidebar-item" @click="setActiveNav('parnetship')" :class="{ 'active' : activeNav === 'parnetship'}">
                <span class="material-icons">diversity_3</span>
                <span>Devenir partenaire</span>
            </a>
                </div>
            </div>

            <!-- Dynamic Submenu Detail View -->
            <div class="sidebar-submenu-detail" :class="{ 'slide-in': showSubmenuDetail }">
                <!-- Header -->
                <div class="submenu-detail-header">
                    <button class="back-btn" @click="closeSubmenu()" title="Retour au menu principal">
                        <span class="material-icons">arrow_back</span>
                    </button>
                    <h3 class="submenu-detail-title" x-text="currentSubmenuTitle"></h3>
                </div>

                <!-- Content -->
                <div class="submenu-detail-content" x-html="submenuContent">
                    <!-- Dynamic content will be inserted here -->
                </div>
            </div>
        </div>
    </div>

    <script>
    function dynamicSidebarNavigation() {
        return {
            showSubmenuDetail: false,
            currentSubmenuTitle: '',
            currentSubmenuId: '',
            submenuContent: '',
            searchActive: false,
            searchQuery: '',
            searchResults: [],
            level2Expanded: {},

            init() {
                // Initialize navigation
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape') {
                        if (this.showSubmenuDetail) {
                            this.closeSubmenu();
                        } else if (this.searchActive) {
                            this.clearSearch();
                        }
                    }
                });
            },

            openSubmenu(element) {
                const submenuId = element.dataset.submenuId;
                const submenuTitle = element.dataset.submenuTitle;

                this.currentSubmenuId = submenuId;
                this.currentSubmenuTitle = submenuTitle;

                // Find the corresponding submenu element
                const submenuElement = document.getElementById(submenuId + '-submenu');
                if (submenuElement) {
                    this.generateSubmenuContent(submenuElement);
                    this.showSubmenuDetail = true;
                }
            },

            generateSubmenuContent(submenuElement) {
                const sectionTitle = submenuElement.dataset.submenuSection || '';
                const submenuItems = submenuElement.querySelectorAll('.submenu-item');

                let content = '';

                if (sectionTitle) {
                    content += `<div class="submenu-section">
                        <div class="submenu-section-title">${sectionTitle}</div>`;
                }

                submenuItems.forEach((item, index) => {
                    const icon = item.querySelector('.material-icons:not(.expand-icon)')?.textContent || 'circle';
                    const title = item.querySelector('span:not(.material-icons):not(.description)')?.textContent || '';
                    const description = item.querySelector('.description')?.textContent || '';
                    const nav = item.dataset.nav || '';
                    const pageTitle = item.dataset.title || title;
                    const hxGet = item.getAttribute('hx-get') || '';
                    const hxTarget = item.getAttribute('hx-target') || '';
                    const hxPushUrl = item.getAttribute('hx-push-url') || '';
                    const level2Id = item.dataset.level2Id || '';

                    const animationDelay = (index + 1) * 0.05;

                    // Check if this is a level 2 trigger (has expand icon and level2Id)
                    if (level2Id && item.querySelector('.expand-icon')) {
                        // This is a level 2 accordion trigger
                        content += `
                            <div class="submenu-level2-container" style="animation-delay: ${animationDelay}s">
                                <button class="submenu-level2-trigger"
                                        @click="toggleLevel2('${level2Id}')"
                                        :class="{ 'expanded': level2Expanded['${level2Id}'] }">
                                    <span class="material-icons">${icon}</span>
                                    <div class="submenu-item-content">
                                        <span class="submenu-item-title">${title}</span>
                                        ${description ? `<span class="submenu-item-description">${description}</span>` : ''}
                                    </div>
                                    <span class="material-icons expand-icon">expand_more</span>
                                </button>
                                <div class="submenu-level2-content" :class="{ 'expanded': level2Expanded['${level2Id}'] }">`;

                        // Find and add level 2 items
                        const level2Container = submenuElement.querySelector(`#${level2Id}-level2`);
                        if (level2Container) {
                            const level2Items = level2Container.querySelectorAll('.submenu-item');
                            level2Items.forEach(level2Item => {
                                const l2Icon = level2Item.querySelector('.material-icons')?.textContent || 'circle';
                                const l2Title = level2Item.querySelector('span:not(.material-icons):not(.description)')?.textContent || '';
                                const l2Description = level2Item.querySelector('.description')?.textContent || '';
                                const l2Nav = level2Item.dataset.nav || '';
                                const l2PageTitle = level2Item.dataset.title || l2Title;
                                const l2HxGet = level2Item.getAttribute('hx-get') || '';
                                const l2HxTarget = level2Item.getAttribute('hx-target') || '';
                                const l2HxPushUrl = level2Item.getAttribute('hx-push-url') || '';

                                content += `
                                    <a href="#" class="submenu-level2-item"
                                       data-nav="${l2Nav}"
                                       data-title="${l2PageTitle}"
                                       ${l2HxGet ? `hx-get="${l2HxGet}"` : ''}
                                       ${l2HxTarget ? `hx-target="${l2HxTarget}"` : ''}
                                       ${l2HxPushUrl ? `hx-push-url="${l2HxPushUrl}"` : ''}
                                       @click.prevent="navigateAndClose('${l2Nav}', '${l2PageTitle}')">
                                        <span class="material-icons">${l2Icon}</span>
                                        <span>${l2Title}</span>
                                    </a>`;
                            });
                        }

                        content += `
                                </div>
                            </div>`;
                    } else {
                        // Regular level 1 item
                        content += `
                            <a href="#" class="submenu-detail-item"
                               style="animation-delay: ${animationDelay}s"
                               data-nav="${nav}"
                               data-title="${pageTitle}"
                               ${hxGet ? `hx-get="${hxGet}"` : ''}
                               ${hxTarget ? `hx-target="${hxTarget}"` : ''}
                               ${hxPushUrl ? `hx-push-url="${hxPushUrl}"` : ''}
                               @click.prevent="navigateAndClose('${nav}', '${pageTitle}')">
                                <span class="material-icons">${icon}</span>
                                <div class="submenu-item-content">
                                    <span class="submenu-item-title">${title}</span>
                                    ${description ? `<span class="submenu-item-description">${description}</span>` : ''}
                                </div>
                            </a>`;
                    }
                });

                if (sectionTitle) {
                    content += '</div>';
                }

                this.submenuContent = content;
            },

            toggleLevel2(level2Id) {
                if (this.level2Expanded[level2Id]) {
                    this.level2Expanded[level2Id] = false;
                } else {
                    // Close all other level 2 items
                    Object.keys(this.level2Expanded).forEach(key => {
                        this.level2Expanded[key] = false;
                    });
                    // Open the clicked one
                    this.level2Expanded[level2Id] = true;
                }
            },

            closeSubmenu() {
                this.showSubmenuDetail = false;
                setTimeout(() => {
                    this.currentSubmenuId = '';
                    this.currentSubmenuTitle = '';
                    this.submenuContent = '';
                }, 300);
            },

            navigateAndClose(nav, title) {
                if (nav) {
                    this.setActiveNav(nav);
                    this.pageTitle = title;
                }
                this.closeSubmenu();
            },

            performSearch() {
                if (!this.searchQuery.trim()) {
                    this.searchResults = [];
                    this.searchActive = false;
                    return;
                }

                this.searchActive = true;
                const query = this.searchQuery.toLowerCase();
                const results = [];

                // Search through all submenu items
                document.querySelectorAll('.submenu-item').forEach(item => {
                    const title = item.querySelector('span:not(.material-icons):not(.description)')?.textContent || '';
                    const description = item.querySelector('.description')?.textContent || '';
                    const icon = item.querySelector('.material-icons')?.textContent || 'circle';

                    // Get parent submenu info
                    const submenu = item.closest('.submenu');
                    const parentSidebarItem = submenu ? document.querySelector(`[data-submenu-id="${submenu.id.replace('-submenu', '')}"]`) : null;
                    const parentTitle = parentSidebarItem ? parentSidebarItem.dataset.submenuTitle : '';

                    if (title.toLowerCase().includes(query) || description.toLowerCase().includes(query)) {
                        results.push({
                            id: item.dataset.nav || Math.random().toString(36).substr(2, 9),
                            title: title,
                            path: parentTitle,
                            icon: icon,
                            element: item
                        });
                    }
                });

                this.searchResults = results.slice(0, 8); // Limit to 8 results
            },

            navigateToItem(result) {
                // Trigger click on the original item
                if (result.element) {
                    result.element.click();
                }
                this.clearSearch();
            },

            clearSearch() {
                this.searchQuery = '';
                this.searchResults = [];
                this.searchActive = false;
                document.getElementById('sidebar-search-input').blur();
            }
        }
    }
    </script>