    <!-- Sidebar -->
    <div class="sidebar" id="sidebar" x-data="sidebarNavigation()" :class="{ 'sidebar-search-active': searchActive }">
        <!-- Sidebar Search Input -->
        <div class="sidebar-search">
            <div class="mdc-text-field mdc-text-field--filled mdc-text-field--no-label mdc-text-field--with-leading-icon">
                <span class="mdc-text-field__icon mdc-text-field__icon--leading material-icons">search</span>
                <input class="mdc-text-field__input"
                       type="text"
                       placeholder="Rechercher dans le menu"
                       id="sidebar-search-input"
                       x-model="searchQuery"
                       @input="performSearch()"
                       @focus="searchActive = true"
                       @blur="if (!searchQuery) searchActive = false">
            </div>
        </div>

        <!-- Search Results -->
        <div class="sidebar-search-results">
            <template x-for="result in searchResults" :key="result.id">
                <a href="#" class="search-result-item" @click="navigateToItem(result)">
                    <span class="material-icons" x-text="result.icon"></span>
                    <div class="search-result-content">
                        <div class="search-result-title" x-text="result.title"></div>
                        <div class="search-result-path" x-text="result.path"></div>
                    </div>
                </a>
            </template>
            <div x-show="searchQuery && searchResults.length === 0" class="search-no-results" style="padding: 20px; text-align: center; color: var(--text-muted);">
                Aucun résultat trouvé
            </div>
        </div>

        <!-- Drill-down Container -->
        <div class="sidebar-drill-down">
            <!-- Main Sidebar View -->
            <div class="sidebar-main-view" :class="{ 'slide-out': showSubmenuDetail }">

        <!-- Section 1: Dashboard & Student Management -->
        <div class="sidebar-section">
            <div class="sidebar-section-title">VIE SCOLAIRE</div>
            <a href="#" class="sidebar-item" @click="setActiveNav('home');" :class="{ 'active' : activeNav === 'home'}"
                hx-get="{% url 'school:home' %}" hx-target="#main-content" hx-push-url="{% url 'school:home' %}">
                <span class="material-icons">dashboard</span>
                <span>Accueil</span>
            </a>
            <a href="#" class="sidebar-item sidebar-item-with-submenu"
            :class="{ 'active' : activeNav === 'eleves' }"
            @click.prevent="openSubmenu('eleves', 'Élèves')">
            <span class="material-icons">groups_2</span>
            <span>Élèves</span>
            <span class="material-icons expand-icon">chevron_right</span>
            </a>
            <div class="submenu" id="eleves-submenu">
            <a href="#" class="submenu-item" @click.prevent="setActiveNav('manage-eleves'); pageTitle = 'Gestion des élèves'"
               hx-get="{% url 'school:students' %}" hx-target="#main-content"
               hx-push-url="{% url 'school:students' %}">
                <span class="material-icons submenu-icon">group_add</span>
                <span>Gestion des élèves</span>
            </a>
            <a href="#" class="submenu-item" @click.prevent="setActiveNav('eleves-cards')">
                <span class="material-icons submenu-icon">format_list_numbered</span>
                <span>Attribution des classes</span>
            </a>
            <a href="#" class="submenu-item" @click.prevent="setActiveNav('eleves-cards')">
                <span class="material-icons submenu-icon">photo_camera</span>
                <span>Gestion des photos</span>
            </a>
            <a href="#" class="submenu-item" @click.prevent="setActiveNav('eleves-cards')">
                <span class="material-icons submenu-icon">event_busy</span>
                <span>Gestion des absences</span>
            </a>
            </div>
            <a href="#" class="sidebar-item sidebar-item-with-submenu"
               :class="{ 'active' : activeNav === 'section2'}"
               @click.prevent="openSubmenu('niveaux', 'Niveaux et Classes')">
                <span class="material-icons">leaderboard</span>
                <span>Niveaux et Classes</span>
                <span class="material-icons expand-icon">chevron_right</span>
            </a>
            <div class="submenu" id="section2-submenu">
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection4')">
                    <span class="material-icons submenu-icon">stairs</span>
                    <span>Niveaux</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection4')">
                    <span class="material-icons submenu-icon">class</span>
                    <span>Classes</span>
                </a>
            </div>
            <a href="#" class="sidebar-item sidebar-item-with-submenu"
               :class="{ 'active' : activeNav === 'section1'}"
               @click.prevent="openSubmenu('comptabilite', 'Comptabilité')">
                <span class="material-icons">request_quote</span>
                <span>Comptabilité</span>
                <span class="material-icons expand-icon">chevron_right</span>
            </a>
            <div class="submenu" id="section1-submenu">
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection1')">
                    <span class="material-icons submenu-icon">history</span>
                    <span>Historique des paiements</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection2')">
                    <span class="material-icons submenu-icon">assessment</span>
                    <span>Rapport des paiements</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection2')">
                    <span class="material-icons submenu-icon">account_circle</span>
                    <span>Gestion des comptables</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection2')">
                    <span class="material-icons submenu-icon">school</span>
                    <span>Frais de scolarité</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection2')">
                    <span class="material-icons submenu-icon">account_balance_wallet</span>
                    <span>Solde et dépenses</span>
                </a>
            </div>
            <a href="#" class="sidebar-item sidebar-item-with-submenu"
               :class="{ 'active' : activeNav === 'section3'}"
               @click.prevent="openSubmenu('notes', 'Notes et Moyennes')">
                <span class="material-icons">calculate</span>
                <span>Notes et Moyennes</span>
                <span class="material-icons expand-icon">chevron_right</span>
            </a>
            <div class="submenu" id="section3-submenu">
                <div class="submenu-item submenu-item-with-submenu" data-submenu="subsection3-submenu">
                    <span class="material-icons submenu-icon">translate</span>
                    <span>Français</span>
                    <span class="material-icons expand-icon">expand_more</span>
                </div>
                <div class="submenu submenu-level-2" id="subsection3-submenu">
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie1')">Fiches de Notation</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Notes</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Moyennes</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Résultats</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Bulletins</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Décisions Annuelles (DFA)</a>
                </div>

                <div class="submenu-item submenu-item-with-submenu" data-submenu="subsection4-submenu">
                    <span class="material-icons submenu-icon">language</span>
                    <span>Arabe</span>
                    <span class="material-icons expand-icon">expand_more</span>
                </div>
                <div class="submenu submenu-level-2" id="subsection4-submenu">
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie1')">Fiches de Notation</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Notes</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Moyennes</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Résultats</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Bulletins</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Décisions Annuelles (DFA)</a>
                </div>
            </div>
        </div>

        <!-- Section 2: Administration & Management -->
        <div class="sidebar-section">
            <div class="sidebar-section-title">ADMINISTRATION</div>
            <div class="sidebar-item sidebar-item-with-submenu" data-submenu="docs-submenu">
                <span class="material-icons">description</span>
                <span>Documents Administratifs</span>
                <span class="material-icons expand-icon">expand_more</span>
            </div>
            <div class="submenu" id="docs-submenu">
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">
                    <span class="material-icons submenu-icon">list_alt</span>
                    <span>Listes de classe</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">
                    <span class="material-icons submenu-icon">calendar_month</span>
                    <span>Registres d'Appel</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">
                    <span class="material-icons submenu-icon">folder_open</span>
                    <span>Fiches Scolaires</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie1')">
                    <span class="material-icons submenu-icon">badge</span>
                    <span>Cartes Scolaires</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie1')">
                    <span class="material-icons submenu-icon">more_horiz</span>
                    <span>Divers</span>
                </a>
            </div>
            <div class="sidebar-item sidebar-item-with-submenu" data-submenu="section4-submenu" :class="{ 'active' : activeNav === 'section4'}">
                <span class="material-icons">home</span>
                <span>Ecole</span>
                <span class="material-icons expand-icon">expand_more</span>
            </div>
            <div class="submenu" id="section4-submenu">
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection4')">
                    <span class="material-icons submenu-icon">info</span>
                    <span>Infos Ecole</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection4')">
                    <span class="material-icons submenu-icon">business</span>
                    <span>Batiments et Salles</span>
                </a>
            </div>
            <div class="sidebar-item sidebar-item-with-submenu" data-submenu="section8-submenu" :class="{ 'active' : activeNav === 'section8'}">
                <span class="material-icons">school</span>
                <span>Pédagogie</span>
                <span class="material-icons expand-icon">expand_more</span>
            </div>
            <div class="submenu" id="section8-submenu">
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection8')">
                    <span class="material-icons submenu-icon">date_range</span>
                    <span>Périodicité • Trimestres</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection8')">
                    <span class="material-icons submenu-icon">subject</span>
                    <span>Matières</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection8')">
                    <span class="material-icons submenu-icon">grade</span>
                    <span>Mentions</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection8')">
                    <span class="material-icons submenu-icon">schedule</span>
                    <span>Emploi du temps</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection8')">
                    <span class="material-icons submenu-icon">assignment</span>
                    <span>Devoirs</span>
                </a>
            </div>
            <div class="sidebar-item sidebar-item-with-submenu" data-submenu="section7-submenu" :class="{ 'active' : activeNav === 'section7'}">
                <span class="material-icons">people</span>
                <span>Ressources Humaines</span>
                <span class="material-icons expand-icon">expand_more</span>
            </div>
            <div class="submenu" id="section7-submenu">
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection7')">
                    <span class="material-icons submenu-icon">person_add</span>
                    <span>Enseignants</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection7')">
                    <span class="material-icons submenu-icon">groups_3</span>
                    <span>Administration</span>
                </a>
                <!--  Gestion des salaires -->
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection7')">
                    <span class="material-icons submenu-icon">paid</span>
                    <span>Gestion des salaires</span>
                </a>
            </div>
        </div>

        <!-- Section 3: Tools & Communication -->
        <div class="sidebar-section">
            <div class="sidebar-section-title">DIVERS</div>
            <a href="#" class="sidebar-item" @click="setActiveNav('messaging')" :class="{ 'active' : activeNav === 'messaging'}">
                <span class="material-icons">mail</span>
                <span>Messagerie</span>
            </a>
            <a href="#" class="sidebar-item" @click="setActiveNav('wallet')" :class="{ 'active' : activeNav === 'wallet'}">
                <span class="material-icons">subscriptions</span>
                <span>Formation EcolePro</span>
            </a>
            <a href="#" class="sidebar-item" @click="setActiveNav('parnetship')" :class="{ 'active' : activeNav === 'parnetship'}">
                <span class="material-icons">diversity_3</span>
                <span>Devenir partenaire</span>
            </a>
                </div>
            </div>

            <!-- Submenu Detail View -->
            <div class="sidebar-submenu-detail" :class="{ 'slide-in': showSubmenuDetail }">
                <!-- Header -->
                <div class="submenu-detail-header">
                    <button class="back-btn" @click="closeSubmenu()" title="Retour au menu principal">
                        <span class="material-icons">arrow_back</span>
                    </button>
                    <h3 class="submenu-detail-title" x-text="currentSubmenuTitle"></h3>
                </div>

                <!-- Content -->
                <div class="submenu-detail-content">
                    <!-- Élèves Submenu -->
                    <div x-show="currentSubmenu === 'eleves'" class="submenu-section">
                        <div class="submenu-section-title">Gestion des Élèves</div>

                        <a href="#" class="submenu-detail-item"
                           :class="{ 'active': activeNav === 'manage-eleves' }"
                           @click.prevent="navigateAndClose('manage-eleves', 'Gestion des élèves')"
                           hx-get="{% url 'school:students' %}" hx-target="#main-content"
                           hx-push-url="{% url 'school:students' %}">
                            <span class="material-icons">group_add</span>
                            <div class="submenu-item-content">
                                <span class="submenu-item-title">Gestion des élèves</span>
                                <span class="submenu-item-description">Ajouter, modifier et gérer les informations des élèves</span>
                            </div>
                        </a>

                        <a href="#" class="submenu-detail-item"
                           :class="{ 'active': activeNav === 'eleves-attribution' }"
                           @click.prevent="navigateAndClose('eleves-attribution', 'Attribution des classes')">
                            <span class="material-icons">format_list_numbered</span>
                            <div class="submenu-item-content">
                                <span class="submenu-item-title">Attribution des classes</span>
                                <span class="submenu-item-description">Assigner les élèves aux différentes classes et niveaux</span>
                            </div>
                        </a>

                        <a href="#" class="submenu-detail-item"
                           :class="{ 'active': activeNav === 'eleves-photos' }"
                           @click.prevent="navigateAndClose('eleves-photos', 'Gestion des photos')">
                            <span class="material-icons">photo_camera</span>
                            <div class="submenu-item-content">
                                <span class="submenu-item-title">Gestion des photos</span>
                                <span class="submenu-item-description">Importer et gérer les photos d'identité des élèves</span>
                            </div>
                        </a>

                        <a href="#" class="submenu-detail-item"
                           :class="{ 'active': activeNav === 'eleves-absences' }"
                           @click.prevent="navigateAndClose('eleves-absences', 'Gestion des absences')">
                            <span class="material-icons">event_busy</span>
                            <div class="submenu-item-content">
                                <span class="submenu-item-title">Gestion des absences</span>
                                <span class="submenu-item-description">Suivre et gérer les absences et retards des élèves</span>
                            </div>
                        </a>
                    </div>

                    <!-- Niveaux et Classes Submenu -->
                    <div x-show="currentSubmenu === 'niveaux'" class="submenu-section">
                        <div class="submenu-section-title">Organisation Scolaire</div>

                        <a href="#" class="submenu-detail-item"
                           :class="{ 'active': activeNav === 'niveaux' }"
                           @click.prevent="navigateAndClose('niveaux', 'Niveaux')">
                            <span class="material-icons">stairs</span>
                            <div class="submenu-item-content">
                                <span class="submenu-item-title">Niveaux</span>
                                <span class="submenu-item-description">Gérer les différents niveaux d'enseignement</span>
                            </div>
                        </a>

                        <a href="#" class="submenu-detail-item"
                           :class="{ 'active': activeNav === 'classes' }"
                           @click.prevent="navigateAndClose('classes', 'Classes')">
                            <span class="material-icons">class</span>
                            <div class="submenu-item-content">
                                <span class="submenu-item-title">Classes</span>
                                <span class="submenu-item-description">Créer et organiser les classes par niveau</span>
                            </div>
                        </a>
                    </div>

                    <!-- Comptabilité Submenu -->
                    <div x-show="currentSubmenu === 'comptabilite'" class="submenu-section">
                        <div class="submenu-section-title">Gestion Financière</div>

                        <a href="#" class="submenu-detail-item"
                           :class="{ 'active': activeNav === 'paiements-historique' }"
                           @click.prevent="navigateAndClose('paiements-historique', 'Historique des paiements')">
                            <span class="material-icons">history</span>
                            <div class="submenu-item-content">
                                <span class="submenu-item-title">Historique des paiements</span>
                                <span class="submenu-item-description">Consulter l'historique complet des paiements</span>
                            </div>
                        </a>

                        <a href="#" class="submenu-detail-item"
                           :class="{ 'active': activeNav === 'rapports-paiements' }"
                           @click.prevent="navigateAndClose('rapports-paiements', 'Rapports des paiements')">
                            <span class="material-icons">assessment</span>
                            <div class="submenu-item-content">
                                <span class="submenu-item-title">Rapports des paiements</span>
                                <span class="submenu-item-description">Générer des rapports financiers détaillés</span>
                            </div>
                        </a>

                        <a href="#" class="submenu-detail-item"
                           :class="{ 'active': activeNav === 'frais-scolarite' }"
                           @click.prevent="navigateAndClose('frais-scolarite', 'Frais de scolarité')">
                            <span class="material-icons">school</span>
                            <div class="submenu-item-content">
                                <span class="submenu-item-title">Frais de scolarité</span>
                                <span class="submenu-item-description">Configurer les frais de scolarité par niveau</span>
                            </div>
                        </a>
                    </div>

                    <!-- Notes et Moyennes Submenu with Level 2 -->
                    <div x-show="currentSubmenu === 'notes'" class="submenu-section">
                        <div class="submenu-section-title">Évaluation Scolaire</div>

                        <!-- Level 2 Accordion: Français -->
                        <div class="submenu-level2-container">
                            <button class="submenu-level2-trigger" @click="toggleLevel2('francais')" :class="{ 'expanded': level2Expanded === 'francais' }">
                                <span class="material-icons">translate</span>
                                <div class="submenu-item-content">
                                    <span class="submenu-item-title">Français</span>
                                    <span class="submenu-item-description">Gestion des notes de français</span>
                                </div>
                                <span class="material-icons expand-icon">expand_more</span>
                            </button>
                            <div class="submenu-level2-content" :class="{ 'expanded': level2Expanded === 'francais' }">
                                <a href="#" class="submenu-level2-item" @click.prevent="navigateAndClose('francais-notes', 'Notes de français')">
                                    <span class="material-icons">grade</span>
                                    <span>Saisie des notes</span>
                                </a>
                                <a href="#" class="submenu-level2-item" @click.prevent="navigateAndClose('francais-moyennes', 'Moyennes français')">
                                    <span class="material-icons">calculate</span>
                                    <span>Calcul des moyennes</span>
                                </a>
                                <a href="#" class="submenu-level2-item" @click.prevent="navigateAndClose('francais-bulletins', 'Bulletins français')">
                                    <span class="material-icons">description</span>
                                    <span>Génération des bulletins</span>
                                </a>
                            </div>
                        </div>

                        <!-- Level 2 Accordion: Arabe -->
                        <div class="submenu-level2-container">
                            <button class="submenu-level2-trigger" @click="toggleLevel2('arabe')" :class="{ 'expanded': level2Expanded === 'arabe' }">
                                <span class="material-icons">language</span>
                                <div class="submenu-item-content">
                                    <span class="submenu-item-title">Arabe</span>
                                    <span class="submenu-item-description">Gestion des notes d'arabe</span>
                                </div>
                                <span class="material-icons expand-icon">expand_more</span>
                            </button>
                            <div class="submenu-level2-content" :class="{ 'expanded': level2Expanded === 'arabe' }">
                                <a href="#" class="submenu-level2-item" @click.prevent="navigateAndClose('arabe-notes', 'Notes d\'arabe')">
                                    <span class="material-icons">grade</span>
                                    <span>Saisie des notes</span>
                                </a>
                                <a href="#" class="submenu-level2-item" @click.prevent="navigateAndClose('arabe-moyennes', 'Moyennes arabe')">
                                    <span class="material-icons">calculate</span>
                                    <span>Calcul des moyennes</span>
                                </a>
                            </div>
                        </div>

                        <!-- Regular Level 1 Item -->
                        <a href="#" class="submenu-detail-item"
                           :class="{ 'active': activeNav === 'notes-generales' }"
                           @click.prevent="navigateAndClose('notes-generales', 'Notes générales')">
                            <span class="material-icons">assignment</span>
                            <div class="submenu-item-content">
                                <span class="submenu-item-title">Notes générales</span>
                                <span class="submenu-item-description">Vue d'ensemble de toutes les notes</span>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
    function sidebarNavigation() {
        return {
            showSubmenuDetail: false,
            currentSubmenu: '',
            currentSubmenuTitle: '',
            level2Expanded: '',
            searchActive: false,
            searchQuery: '',
            searchResults: [],

            // Menu items database for search
            menuItems: [
                // Élèves
                { id: 'manage-eleves', title: 'Gestion des élèves', path: 'Élèves', icon: 'group_add', action: () => this.navigateAndClose('manage-eleves', 'Gestion des élèves') },
                { id: 'eleves-attribution', title: 'Attribution des classes', path: 'Élèves', icon: 'format_list_numbered', action: () => this.navigateAndClose('eleves-attribution', 'Attribution des classes') },
                { id: 'eleves-photos', title: 'Gestion des photos', path: 'Élèves', icon: 'photo_camera', action: () => this.navigateAndClose('eleves-photos', 'Gestion des photos') },
                { id: 'eleves-absences', title: 'Gestion des absences', path: 'Élèves', icon: 'event_busy', action: () => this.navigateAndClose('eleves-absences', 'Gestion des absences') },

                // Niveaux et Classes
                { id: 'niveaux', title: 'Niveaux', path: 'Niveaux et Classes', icon: 'stairs', action: () => this.navigateAndClose('niveaux', 'Niveaux') },
                { id: 'classes', title: 'Classes', path: 'Niveaux et Classes', icon: 'class', action: () => this.navigateAndClose('classes', 'Classes') },

                // Comptabilité
                { id: 'paiements-historique', title: 'Historique des paiements', path: 'Comptabilité', icon: 'history', action: () => this.navigateAndClose('paiements-historique', 'Historique des paiements') },
                { id: 'rapports-paiements', title: 'Rapports des paiements', path: 'Comptabilité', icon: 'assessment', action: () => this.navigateAndClose('rapports-paiements', 'Rapports des paiements') },
                { id: 'frais-scolarite', title: 'Frais de scolarité', path: 'Comptabilité', icon: 'school', action: () => this.navigateAndClose('frais-scolarite', 'Frais de scolarité') },

                // Notes - Level 2 items
                { id: 'francais-notes', title: 'Saisie des notes', path: 'Notes > Français', icon: 'grade', action: () => this.navigateAndClose('francais-notes', 'Notes de français') },
                { id: 'francais-moyennes', title: 'Calcul des moyennes', path: 'Notes > Français', icon: 'calculate', action: () => this.navigateAndClose('francais-moyennes', 'Moyennes français') },
                { id: 'francais-bulletins', title: 'Génération des bulletins', path: 'Notes > Français', icon: 'description', action: () => this.navigateAndClose('francais-bulletins', 'Bulletins français') },
                { id: 'arabe-notes', title: 'Saisie des notes', path: 'Notes > Arabe', icon: 'grade', action: () => this.navigateAndClose('arabe-notes', 'Notes d\'arabe') },
                { id: 'arabe-moyennes', title: 'Calcul des moyennes', path: 'Notes > Arabe', icon: 'calculate', action: () => this.navigateAndClose('arabe-moyennes', 'Moyennes arabe') },
                { id: 'notes-generales', title: 'Notes générales', path: 'Notes', icon: 'assignment', action: () => this.navigateAndClose('notes-generales', 'Notes générales') },
            ],

            init() {
                // Initialize navigation
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape') {
                        if (this.showSubmenuDetail) {
                            this.closeSubmenu();
                        } else if (this.searchActive) {
                            this.clearSearch();
                        }
                    }
                });
            },

            openSubmenu(submenu, title) {
                this.currentSubmenu = submenu;
                this.currentSubmenuTitle = title;
                this.showSubmenuDetail = true;
                this.level2Expanded = '';
            },

            closeSubmenu() {
                this.showSubmenuDetail = false;
                this.level2Expanded = '';
                setTimeout(() => {
                    this.currentSubmenu = '';
                    this.currentSubmenuTitle = '';
                }, 300);
            },

            toggleLevel2(section) {
                this.level2Expanded = this.level2Expanded === section ? '' : section;
            },

            navigateAndClose(nav, title) {
                this.setActiveNav(nav);
                this.pageTitle = title;
                this.closeSubmenu();
            },

            performSearch() {
                if (!this.searchQuery.trim()) {
                    this.searchResults = [];
                    this.searchActive = false;
                    return;
                }

                this.searchActive = true;
                const query = this.searchQuery.toLowerCase();

                this.searchResults = this.menuItems.filter(item =>
                    item.title.toLowerCase().includes(query) ||
                    item.path.toLowerCase().includes(query)
                ).slice(0, 8); // Limit to 8 results
            },

            navigateToItem(item) {
                item.action();
                this.clearSearch();
            },

            clearSearch() {
                this.searchQuery = '';
                this.searchResults = [];
                this.searchActive = false;
                document.getElementById('sidebar-search-input').blur();
            }
        }
    }
    </script>