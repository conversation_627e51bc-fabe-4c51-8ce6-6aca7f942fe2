    <!-- Sidebar -->
    <div class="sidebar" id="sidebar" x-data="sidebarNavigation()">
        <!-- Main Sidebar View -->
        <div class="sidebar-main-view" x-show="!showSubmenuDetail" x-transition>
            <!-- Sidebar Search Input -->
            <div class="sidebar-search">
                <div class="mdc-text-field mdc-text-field--filled mdc-text-field--no-label mdc-text-field--with-leading-icon">
                    <span class="mdc-text-field__icon mdc-text-field__icon--leading material-icons">search</span>
                    <input class="mdc-text-field__input"
                           type="text"
                           placeholder="Rechercher dans le menu"
                           id="sidebar-search-input">
                </div>
            </div>

        <!-- Section 1: Dashboard & Student Management -->
        <div class="sidebar-section">
            <div class="sidebar-section-title">VIE SCOLAIRE</div>
            <a href="#" class="sidebar-item" @click="setActiveNav('home');" :class="{ 'active' : activeNav === 'home'}"
                hx-get="{% url 'school:home' %}" hx-target="#main-content" hx-push-url="{% url 'school:home' %}">
                <span class="material-icons">dashboard</span>
                <span>Accueil</span>
            </a>
            <a href="#" class="sidebar-item sidebar-item-with-submenu"
            :class="{ 'active' : activeNav === 'eleves' }"
            @click.prevent="openSubmenu('eleves', 'Élèves')">
            <span class="material-icons">groups_2</span>
            <span>Élèves</span>
            <span class="material-icons expand-icon">chevron_right</span>
            </a>
            <div class="submenu" id="eleves-submenu">
            <a href="#" class="submenu-item" @click.prevent="setActiveNav('manage-eleves'); pageTitle = 'Gestion des élèves'"
               hx-get="{% url 'school:students' %}" hx-target="#main-content"
               hx-push-url="{% url 'school:students' %}">
                <span class="material-icons submenu-icon">group_add</span>
                <span>Gestion des élèves</span>
            </a>
            <a href="#" class="submenu-item" @click.prevent="setActiveNav('eleves-cards')">
                <span class="material-icons submenu-icon">format_list_numbered</span>
                <span>Attribution des classes</span>
            </a>
            <a href="#" class="submenu-item" @click.prevent="setActiveNav('eleves-cards')">
                <span class="material-icons submenu-icon">photo_camera</span>
                <span>Gestion des photos</span>
            </a>
            <a href="#" class="submenu-item" @click.prevent="setActiveNav('eleves-cards')">
                <span class="material-icons submenu-icon">event_busy</span>
                <span>Gestion des absences</span>
            </a>
            </div>
            <a href="#" class="sidebar-item sidebar-item-with-submenu"
               :class="{ 'active' : activeNav === 'section2'}"
               @click.prevent="openSubmenu('niveaux', 'Niveaux et Classes')">
                <span class="material-icons">leaderboard</span>
                <span>Niveaux et Classes</span>
                <span class="material-icons expand-icon">chevron_right</span>
            </a>
            <div class="submenu" id="section2-submenu">
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection4')">
                    <span class="material-icons submenu-icon">stairs</span>
                    <span>Niveaux</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection4')">
                    <span class="material-icons submenu-icon">class</span>
                    <span>Classes</span>
                </a>
            </div>
            <a href="#" class="sidebar-item sidebar-item-with-submenu"
               :class="{ 'active' : activeNav === 'section1'}"
               @click.prevent="showSubmenuDetail = true; currentSubmenu = 'comptabilite'; currentSubmenuTitle = 'Comptabilité'">
                <span class="material-icons">request_quote</span>
                <span>Comptabilité</span>
                <span class="material-icons expand-icon">chevron_right</span>
            </a>
            <div class="submenu" id="section1-submenu">
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection1')">
                    <span class="material-icons submenu-icon">history</span>
                    <span>Historique des paiements</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection2')">
                    <span class="material-icons submenu-icon">assessment</span>
                    <span>Rapport des paiements</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection2')">
                    <span class="material-icons submenu-icon">account_circle</span>
                    <span>Gestion des comptables</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection2')">
                    <span class="material-icons submenu-icon">school</span>
                    <span>Frais de scolarité</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection2')">
                    <span class="material-icons submenu-icon">account_balance_wallet</span>
                    <span>Solde et dépenses</span>
                </a>
            </div>
            <a href="#" class="sidebar-item sidebar-item-with-submenu"
               :class="{ 'active' : activeNav === 'section3'}"
               @click.prevent="showSubmenuDetail = true; currentSubmenu = 'notes'; currentSubmenuTitle = 'Notes et Moyennes'">
                <span class="material-icons">calculate</span>
                <span>Notes et Moyennes</span>
                <span class="material-icons expand-icon">chevron_right</span>
            </a>
            <div class="submenu" id="section3-submenu">
                <div class="submenu-item submenu-item-with-submenu" data-submenu="subsection3-submenu">
                    <span class="material-icons submenu-icon">translate</span>
                    <span>Français</span>
                    <span class="material-icons expand-icon">expand_more</span>
                </div>
                <div class="submenu submenu-level-2" id="subsection3-submenu">
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie1')">Fiches de Notation</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Notes</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Moyennes</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Résultats</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Bulletins</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Décisions Annuelles (DFA)</a>
                </div>

                <div class="submenu-item submenu-item-with-submenu" data-submenu="subsection4-submenu">
                    <span class="material-icons submenu-icon">language</span>
                    <span>Arabe</span>
                    <span class="material-icons expand-icon">expand_more</span>
                </div>
                <div class="submenu submenu-level-2" id="subsection4-submenu">
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie1')">Fiches de Notation</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Notes</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Moyennes</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Résultats</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Bulletins</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Décisions Annuelles (DFA)</a>
                </div>
            </div>
        </div>

        <!-- Section 2: Administration & Management -->
        <div class="sidebar-section">
            <div class="sidebar-section-title">ADMINISTRATION</div>
            <div class="sidebar-item sidebar-item-with-submenu" data-submenu="docs-submenu">
                <span class="material-icons">description</span>
                <span>Documents Administratifs</span>
                <span class="material-icons expand-icon">expand_more</span>
            </div>
            <div class="submenu" id="docs-submenu">
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">
                    <span class="material-icons submenu-icon">list_alt</span>
                    <span>Listes de classe</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">
                    <span class="material-icons submenu-icon">calendar_month</span>
                    <span>Registres d'Appel</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">
                    <span class="material-icons submenu-icon">folder_open</span>
                    <span>Fiches Scolaires</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie1')">
                    <span class="material-icons submenu-icon">badge</span>
                    <span>Cartes Scolaires</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie1')">
                    <span class="material-icons submenu-icon">more_horiz</span>
                    <span>Divers</span>
                </a>
            </div>
            <div class="sidebar-item sidebar-item-with-submenu" data-submenu="section4-submenu" :class="{ 'active' : activeNav === 'section4'}">
                <span class="material-icons">home</span>
                <span>Ecole</span>
                <span class="material-icons expand-icon">expand_more</span>
            </div>
            <div class="submenu" id="section4-submenu">
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection4')">
                    <span class="material-icons submenu-icon">info</span>
                    <span>Infos Ecole</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection4')">
                    <span class="material-icons submenu-icon">business</span>
                    <span>Batiments et Salles</span>
                </a>
            </div>
            <div class="sidebar-item sidebar-item-with-submenu" data-submenu="section8-submenu" :class="{ 'active' : activeNav === 'section8'}">
                <span class="material-icons">school</span>
                <span>Pédagogie</span>
                <span class="material-icons expand-icon">expand_more</span>
            </div>
            <div class="submenu" id="section8-submenu">
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection8')">
                    <span class="material-icons submenu-icon">date_range</span>
                    <span>Périodicité • Trimestres</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection8')">
                    <span class="material-icons submenu-icon">subject</span>
                    <span>Matières</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection8')">
                    <span class="material-icons submenu-icon">grade</span>
                    <span>Mentions</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection8')">
                    <span class="material-icons submenu-icon">schedule</span>
                    <span>Emploi du temps</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection8')">
                    <span class="material-icons submenu-icon">assignment</span>
                    <span>Devoirs</span>
                </a>
            </div>
            <a href="#" class="sidebar-item sidebar-item-with-submenu"
               :class="{ 'active' : activeNav === 'section7'}"
               @click.prevent="showSubmenuDetail = true; currentSubmenu = 'rh'; currentSubmenuTitle = 'Ressources Humaines'">
                <span class="material-icons">people</span>
                <span>Ressources Humaines</span>
                <span class="material-icons expand-icon">chevron_right</span>
            </a>
            <div class="submenu" id="section7-submenu">
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection7')">
                    <span class="material-icons submenu-icon">person_add</span>
                    <span>Enseignants</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection7')">
                    <span class="material-icons submenu-icon">groups_3</span>
                    <span>Administration</span>
                </a>
                <!--  Gestion des salaires -->
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection7')">
                    <span class="material-icons submenu-icon">paid</span>
                    <span>Gestion des salaires</span>
                </a>
            </div>
        </div>

        <!-- Section 3: Tools & Communication -->
        <div class="sidebar-section">
            <div class="sidebar-section-title">DIVERS</div>
            <a href="#" class="sidebar-item" @click="setActiveNav('messaging')" :class="{ 'active' : activeNav === 'messaging'}">
                <span class="material-icons">mail</span>
                <span>Messagerie</span>
            </a>
            <a href="#" class="sidebar-item" @click="setActiveNav('wallet')" :class="{ 'active' : activeNav === 'wallet'}">
                <span class="material-icons">subscriptions</span>
                <span>Formation EcolePro</span>
            </a>
            <a href="#" class="sidebar-item" @click="setActiveNav('parnetship')" :class="{ 'active' : activeNav === 'parnetship'}">
                <span class="material-icons">diversity_3</span>
                <span>Devenir partenaire</span>
            </a>
        </div>
        </div>

        <!-- Submenu Detail View -->
        <div class="sidebar-submenu-detail" x-show="showSubmenuDetail" x-transition>
            <!-- Back Button Header -->
            <div class="submenu-detail-header">
                <button class="back-btn" @click="showSubmenuDetail = false" title="Retour au menu principal">
                    <span class="material-icons">arrow_back</span>
                </button>
                <div class="submenu-header-content">
                    <h3 class="submenu-detail-title" x-text="currentSubmenuTitle"></h3>
                    <div class="submenu-breadcrumb">Menu > <span x-text="currentSubmenuTitle"></span></div>
                </div>
            </div>

            <!-- Submenu Search -->
            <div class="submenu-search" x-show="showSubmenuDetail">
                <div class="mdc-text-field mdc-text-field--filled mdc-text-field--no-label mdc-text-field--with-leading-icon submenu-search-field">
                    <span class="mdc-text-field__icon mdc-text-field__icon--leading material-icons">search</span>
                    <input class="mdc-text-field__input"
                           type="text"
                           placeholder="Rechercher dans ce menu..."
                           x-model="submenuSearchQuery"
                           @input="filterSubmenuItems()">
                </div>
            </div>

            <!-- Submenu Items -->
            <div class="submenu-detail-content">
                <!-- Élèves Submenu -->
                <div x-show="currentSubmenu === 'eleves'" class="submenu-section">
                    <div class="submenu-section-title">Gestion des Élèves</div>
                    <a href="#" class="submenu-detail-item"
                       :class="{ 'active': activeNav === 'manage-eleves' }"
                       @click.prevent="setActiveNav('manage-eleves'); pageTitle = 'Gestion des élèves'; showSubmenuDetail = false"
                       hx-get="{% url 'school:students' %}" hx-target="#main-content"
                       hx-push-url="{% url 'school:students' %}">
                        <span class="material-icons">group_add</span>
                        <div class="submenu-item-content">
                            <span class="submenu-item-title">Gestion des élèves</span>
                            <span class="submenu-item-description">Ajouter, modifier et gérer les élèves</span>
                        </div>
                    </a>
                    <a href="#" class="submenu-detail-item"
                       :class="{ 'active': activeNav === 'eleves-attribution' }"
                       @click.prevent="setActiveNav('eleves-attribution'); showSubmenuDetail = false">
                        <span class="material-icons">format_list_numbered</span>
                        <div class="submenu-item-content">
                            <span class="submenu-item-title">Attribution des classes</span>
                            <span class="submenu-item-description">Assigner les élèves aux classes</span>
                        </div>
                    </a>
                    <a href="#" class="submenu-detail-item"
                       :class="{ 'active': activeNav === 'eleves-photos' }"
                       @click.prevent="setActiveNav('eleves-photos'); showSubmenuDetail = false">
                        <span class="material-icons">photo_camera</span>
                        <div class="submenu-item-content">
                            <span class="submenu-item-title">Gestion des photos</span>
                            <span class="submenu-item-description">Importer et gérer les photos d'élèves</span>
                        </div>
                    </a>
                    <a href="#" class="submenu-detail-item"
                       :class="{ 'active': activeNav === 'eleves-absences' }"
                       @click.prevent="setActiveNav('eleves-absences'); showSubmenuDetail = false">
                        <span class="material-icons">event_busy</span>
                        <div class="submenu-item-content">
                            <span class="submenu-item-title">Gestion des absences</span>
                            <span class="submenu-item-description">Suivre et gérer les absences</span>
                        </div>
                    </a>
                </div>

                <!-- Niveaux et Classes Submenu -->
                <div x-show="currentSubmenu === 'niveaux'">
                    <a href="#" class="submenu-detail-item" @click.prevent="setActiveNav('subsection4'); showSubmenuDetail = false">
                        <span class="material-icons">stairs</span>
                        <span>Niveaux</span>
                    </a>
                    <a href="#" class="submenu-detail-item" @click.prevent="setActiveNav('subsection4'); showSubmenuDetail = false">
                        <span class="material-icons">class</span>
                        <span>Classes</span>
                    </a>
                </div>

                <!-- Comptabilité Submenu -->
                <div x-show="currentSubmenu === 'comptabilite'">
                    <a href="#" class="submenu-detail-item" @click.prevent="setActiveNav('subsection1'); showSubmenuDetail = false">
                        <span class="material-icons">history</span>
                        <span>Historique des paiements</span>
                    </a>
                    <a href="#" class="submenu-detail-item" @click.prevent="setActiveNav('subsection2'); showSubmenuDetail = false">
                        <span class="material-icons">assessment</span>
                        <span>Rapport des paiements</span>
                    </a>
                    <a href="#" class="submenu-detail-item" @click.prevent="setActiveNav('subsection2'); showSubmenuDetail = false">
                        <span class="material-icons">account_circle</span>
                        <span>Gestion des comptables</span>
                    </a>
                    <a href="#" class="submenu-detail-item" @click.prevent="setActiveNav('subsection2'); showSubmenuDetail = false">
                        <span class="material-icons">school</span>
                        <span>Frais de scolarité</span>
                    </a>
                    <a href="#" class="submenu-detail-item" @click.prevent="setActiveNav('subsection2'); showSubmenuDetail = false">
                        <span class="material-icons">account_balance_wallet</span>
                        <span>Solde et dépenses</span>
                    </a>
                </div>

                <!-- Notes et Moyennes Submenu -->
                <div x-show="currentSubmenu === 'notes'">
                    <a href="#" class="submenu-detail-item" @click.prevent="setActiveNav('subsection3'); showSubmenuDetail = false">
                        <span class="material-icons">translate</span>
                        <span>Français</span>
                    </a>
                    <a href="#" class="submenu-detail-item" @click.prevent="setActiveNav('subsection3'); showSubmenuDetail = false">
                        <span class="material-icons">language</span>
                        <span>Arabe</span>
                    </a>
                    <a href="#" class="submenu-detail-item" @click.prevent="setActiveNav('subsection3'); showSubmenuDetail = false">
                        <span class="material-icons">calculate</span>
                        <span>Mathématiques</span>
                    </a>
                    <a href="#" class="submenu-detail-item" @click.prevent="setActiveNav('subsection3'); showSubmenuDetail = false">
                        <span class="material-icons">science</span>
                        <span>Sciences</span>
                    </a>
                </div>

                <!-- Ressources Humaines Submenu -->
                <div x-show="currentSubmenu === 'rh'">
                    <a href="#" class="submenu-detail-item" @click.prevent="setActiveNav('subsection7'); showSubmenuDetail = false">
                        <span class="material-icons">person_add</span>
                        <span>Enseignants</span>
                    </a>
                    <a href="#" class="submenu-detail-item" @click.prevent="setActiveNav('subsection7'); showSubmenuDetail = false">
                        <span class="material-icons">groups_3</span>
                        <span>Administration</span>
                    </a>
                    <a href="#" class="submenu-detail-item" @click.prevent="setActiveNav('subsection7'); showSubmenuDetail = false">
                        <span class="material-icons">paid</span>
                        <span>Gestion des salaires</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
    function sidebarNavigation() {
        return {
            showSubmenuDetail: false,
            currentSubmenu: '',
            currentSubmenuTitle: '',
            submenuSearchQuery: '',

            init() {
                // Initialize sidebar navigation
                // Add keyboard navigation support
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape' && this.showSubmenuDetail) {
                        this.showSubmenuDetail = false;
                    }
                });
            },

            filterSubmenuItems() {
                // Filter submenu items based on search query
                const query = this.submenuSearchQuery.toLowerCase();
                const items = document.querySelectorAll('.submenu-detail-item');

                items.forEach(item => {
                    const title = item.querySelector('.submenu-item-title')?.textContent.toLowerCase() || '';
                    const description = item.querySelector('.submenu-item-description')?.textContent.toLowerCase() || '';
                    const text = item.textContent.toLowerCase();

                    if (title.includes(query) || description.includes(query) || text.includes(query)) {
                        item.style.display = 'flex';
                    } else {
                        item.style.display = 'none';
                    }
                });
            },

            openSubmenu(submenu, title) {
                this.showSubmenuDetail = true;
                this.currentSubmenu = submenu;
                this.currentSubmenuTitle = title;
                this.submenuSearchQuery = ''; // Reset search when opening new submenu
            }
        }
    }
    </script>