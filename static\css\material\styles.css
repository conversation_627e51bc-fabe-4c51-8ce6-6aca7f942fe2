/* ========================================
   CSS CUSTOM PROPERTIES (COLOR PALETTE)
   ======================================== */
:root {
    /* Primary Colors */
    --primary-color: #1976d2;
    --primary-light: #42a5f5;
    --primary-dark: #1565c0;
    --primary-gradient: linear-gradient(135deg, var(--primary-color), var(--primary-light));

    /* Secondary Colors */
    --secondary-color: #03dac6;
    --secondary-light: #66fff9;
    --secondary-dark: #00a896;

    /* Background Colors */
    --background-primary: #f5f5f5;
    --background-secondary: #fafafa;
    --background-tertiary: #f0f0f0;
    --background-surface: #ffffff;
    --background-overlay: rgba(255, 255, 255, 0.9);

    /* Text Colors */
    --text-primary: #212121;
    --text-secondary: #757575;
    --text-disabled: #9e9e9e;
    --text-on-primary: #ffffff;
    --text-on-surface: #212121;

    /* Border Colors */
    --border-light: #e0e0e0;
    --border-medium: #bdbdbd;
    --border-dark: #9e9e9e;

    /* State Colors */
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --error-color: #f44336;
    --info-color: #2196f3;

    /* Surface Colors */
    --surface-hover: #f5f5f5;
    --surface-selected: #e3f2fd;
    --surface-disabled: #f5f5f5;

    /* Shadow Colors */
    --shadow-light: rgba(0, 0, 0, 0.1);
    --shadow-medium: rgba(0, 0, 0, 0.2);
    --shadow-dark: rgba(0, 0, 0, 0.3);

    /* Opacity Values */
    --opacity-disabled: 0.6;
    --opacity-hover: 0.8;
    --opacity-pressed: 0.12;

    /* Primary Color Variants with Opacity */
    --primary-alpha-8: rgba(25, 118, 210, 0.08);
    --primary-alpha-10: rgba(25, 118, 210, 0.10);
    --primary-alpha-12: rgba(25, 118, 210, 0.12);
    --primary-alpha-16: rgba(25, 118, 210, 0.16);
    --primary-alpha-24: rgba(25, 118, 210, 0.24);
}

/* Dark Mode Color Palette */
[data-theme="dark"] {
    /* Background Colors */
    --background-primary: #121212;
    --background-secondary: #1e1e1e;
    --background-tertiary: #2a2a2a;
    --background-surface: #242424;
    --background-overlay: rgba(0, 0, 0, 0.9);

    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --text-disabled: #666666;
    --text-on-surface: #ffffff;

    /* Border Colors */
    --border-light: #333333;
    --border-medium: #444444;
    --border-dark: #555555;

    /* Surface Colors */
    --surface-hover: #2c2c2c;
    --surface-selected: #1a237e;
    --surface-disabled: #2c2c2c;

    /* Shadow Colors */
    --shadow-light: rgba(0, 0, 0, 0.3);
    --shadow-medium: rgba(0, 0, 0, 0.5);
    --shadow-dark: rgba(0, 0, 0, 0.7);

    /* Primary Color Variants with Opacity for Dark Mode */
    --primary-alpha-8: rgba(66, 165, 245, 0.08);
    --primary-alpha-12: rgba(66, 165, 245, 0.12);
    --primary-alpha-16: rgba(66, 165, 245, 0.16);
    --primary-alpha-24: rgba(66, 165, 245, 0.24);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    scrollbar-width: thin;
    scrollbar-color: #ccc #f5f5f5;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* .mdc-text-field--outlined:not(.mdc-text-field--disabled) 
.mdc-notched-outline__leading, 
.mdc-text-field--outlined:not(.mdc-text-field--disabled) 
.mdc-notched-outline__notch, 
.mdc-text-field--outlined:not(.mdc-text-field--disabled) 
.mdc-notched-outline__trailing {
    border-color: rgba(0, 0, 0, 0.38);
} */

/* Bottom App Bar (Mobile) */
.bottom-app-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--background-surface);
    border-top: 1px solid var(--border-light);
    display: none;
    z-index: 1500;
    padding: 2px 0;
    box-shadow: 0 -2px 8px var(--shadow-light);
    justify-content: space-around;
    align-items: center;
}

.bottom-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 4px 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 6px;
    min-width: 56px;
    position: relative;
    overflow: hidden;
}

.bottom-nav-item:hover {
    background-color: var(--surface-hover);
}

.bottom-nav-item.active {
    background-color: var(--surface-selected);
}

.bottom-nav-item .material-icons {
    font-size: 20px;
    color: var(--text-secondary);
    margin-bottom: 1px;
    transition: color 0.2s ease;
}

.bottom-nav-item.active .material-icons {
    color: var(--primary-color);
}

.bottom-nav-label {
    font-size: 10px;
    font-weight: 500;
    color: var(--text-secondary);
    transition: color 0.2s ease;
    text-align: center;
    line-height: 1;
}

.bottom-nav-item.active .bottom-nav-label {
    color: var(--primary-color);
}

html, body {
    overflow-x: hidden; /* Prevent horizontal page scrolling */
}

body {
    font-family: 'Roboto', sans-serif;
    background-color: var(--background-primary);
    display: flex;
    height: 100vh;
    max-width: 100vw; /* Ensure body doesn't exceed viewport width */
}

/* Page Preloader Styles */
.page-preloader {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-gradient);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
    visibility: visible;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.page-preloader.hidden {
    opacity: 0;
    visibility: hidden;
}

.preloader-content {
    text-align: center;
    color: var(--text-on-primary);
}

.preloader-content .mdc-circular-progress__determinate-track,
.preloader-content .mdc-circular-progress__determinate-circle,
.preloader-content .mdc-circular-progress__indeterminate-circle-graphic circle {
    stroke: var(--text-on-primary);
}

.preloader-text {
    margin-top: 24px;
    font-size: 16px;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.9);
    animation: pulse-text 2s ease-in-out infinite;
}

@keyframes pulse-text {
    0%, 100% { opacity: 0.9; }
    50% { opacity: 0.6; }
}

/* Loading Overlay Styles */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--background-overlay);
    backdrop-filter: blur(2px);
    z-index: 3000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.loading-overlay.active {
    opacity: 1;
    visibility: visible;
}

.loading-content {
    text-align: center;
    background: var(--background-surface);
    padding: 32px;
    border-radius: 12px;
    box-shadow: 0 8px 32px var(--shadow-light);
    min-width: 200px;
}

.loading-text {
    margin-top: 16px;
    font-size: 14px;
    font-weight: 400;
    color: var(--text-secondary);
}

/* Material Design Circular Progress Customization */
.mdc-circular-progress {
    color: var(--text-on-primary);
}

.loading-overlay .mdc-circular-progress {
    color: var(--primary-color);
}

.mdc-circular-progress--large {
    width: 64px;
    height: 64px;
}

.mdc-circular-progress--medium {
    width: 40px;
    height: 40px;
}

/* App Bar Styles */
.app-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: var(--primary-gradient);
    color: var(--text-on-primary);
    height: 56px;
    display: flex;
    align-items: center;
    padding: 0 16px;
    box-shadow: 0 2px 4px var(--shadow-medium);
}

.app-bar .material-icons {
    margin-right: 16px;
    cursor: pointer;
}

.app-bar h1 {
    font-size: 20px;
    font-weight: 500;
    flex: 1;
}

.app-bar .actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Animated Hamburger Menu */
#menu-btn {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    user-select: none;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin-right: 8px;
}

#menu-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

#menu-btn:active {
    background-color: rgba(255, 255, 255, 0.2);
    transform: scale(0.95);
}

/* Icon transformation animation */
#menu-btn.menu-active {
    transform: rotate(180deg);
}

/* Smooth transition for icon change */
#menu-btn .material-icons {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 24px;
}

.app-bar .material-icons {
    margin: 0;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
    overflow: hidden;
}

.app-bar .material-icons:hover {
    background-color: rgba(255,255,255,0.1);
}

/* Overlay Styles */
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: var(--background-surface);
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 100%;
    max-height: 80vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform 0.3s ease;
    display: flex;
    flex-direction: column;
}

.modal-overlay.active .modal {
    transform: scale(1);
}

.modal-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: 18px;
    font-weight: 500;
    color: var(--text-primary);
    margin: 0;
}

.modal-close {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.2s;
    position: relative;
    overflow: hidden;
}

.modal-close:hover {
    background-color: var(--surface-hover);
}

.modal-content {
    padding: 20px 24px;
    flex: 1;
    overflow-y: auto;
    color: var(--text-primary);
}

.modal-actions {
    padding: 16px 24px 20px;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    border-top: 1px solid var(--border-light);
}

/* Mobile Modal Styles */
@media (max-width: 768px) {
    .modal-overlay {
        padding: 8px;
    }

    .modal {
        max-width: 100%;
        max-height: 90vh;
    }

    .modal-header {
        padding: 16px 20px 12px;
    }

    .modal-title {
        font-size: 16px;
    }

    .modal-content {
        padding: 16px 20px;
    }

    .modal-actions {
        padding: 12px 20px 16px;
        flex-direction: column-reverse;
        gap: 8px;
    }

    .modal-actions .mdc-button {
        width: 100%;
        justify-content: center;
    }
}

/* Modal Dark Mode Support */
[data-theme="dark"] .modal {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
}

[data-theme="dark"] .modal-close:hover {
    background-color: var(--surface-hover);
}

/* ========================================
   BUTTON STYLES WITH COLOR PALETTE
   ======================================== */

/* Material Design Button Base Styles */
.mdc-button {
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0.0892857143em;
    text-decoration: none;
    text-transform: uppercase;
    border-radius: 4px;
    padding: 0 16px;
    height: 36px;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    transition: all 0.2s ease;
    min-width: 64px;
    box-sizing: border-box;
}

/* Text Button (Default) */
.mdc-button {
    background-color: transparent;
    color: var(--primary-color);
}

.mdc-button:hover {
    background-color: var(--primary-alpha-8);
}

.mdc-button:focus {
    background-color: var(--primary-alpha-12);
    outline: none;
}

.mdc-button:active {
    background-color: var(--primary-alpha-16);
}

.mdc-button:disabled {
    color: var(--text-disabled);
    background-color: transparent;
    cursor: not-allowed;
    opacity: var(--opacity-disabled);
}

/* Outlined Button */
.mdc-button--outlined {
    border: 1px solid var(--primary-color);
    background-color: transparent;
    color: var(--primary-color);
}

.mdc-button--outlined:hover {
    background-color: var(--primary-alpha-8);
    border-color: var(--primary-light);
}

.mdc-button--outlined:focus {
    background-color: var(--primary-alpha-12);
    border-color: var(--primary-light);
    outline: none;
}

.mdc-button--outlined:active {
    background-color: var(--primary-alpha-16);
    border-color: var(--primary-dark);
}

.mdc-button--outlined:disabled {
    border-color: var(--border-medium);
    color: var(--text-disabled);
    background-color: transparent;
    cursor: not-allowed;
    opacity: var(--opacity-disabled);
}

/* Raised Button (Filled) */
.mdc-button--raised {
    background-color: var(--primary-color);
    color: var(--text-on-primary);
    box-shadow: 0 2px 4px var(--shadow-light);
}

.mdc-button--raised:hover {
    background-color: var(--primary-light);
    box-shadow: 0 4px 8px var(--shadow-medium);
}

.mdc-button--raised:focus {
    background-color: var(--primary-light);
    box-shadow: 0 4px 8px var(--shadow-medium);
    outline: none;
}

.mdc-button--raised:active {
    background-color: var(--primary-dark);
    box-shadow: 0 2px 4px var(--shadow-light);
}

.mdc-button--raised:disabled {
    background-color: var(--surface-disabled);
    color: var(--text-disabled);
    box-shadow: none;
    cursor: not-allowed;
    opacity: var(--opacity-disabled);
}

/* Button with Icons */
.mdc-button .material-icons {
    font-size: 18px;
    margin-right: 8px;
    margin-left: -4px;
}

.mdc-button .material-icons:last-child {
    margin-right: -4px;
    margin-left: 8px;
}

/* Button Ripple Effect */
.mdc-button__ripple {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: inherit;
    pointer-events: none;
}

/* Button Label */
.mdc-button__label {
    position: relative;
    z-index: 1;
}

/* ========================================
   SNACKBAR STYLES WITH COLOR PALETTE
   ======================================== */

/* Snackbar Base Styles */
.mdc-snackbar {
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    z-index: 3000;
    margin: 8px;
    max-width: 568px;
    min-width: 344px;
    opacity: 0;
    transform: translateX(-50%) translateY(100%);
    transition: opacity 0.15s ease, transform 0.15s ease;
}

.mdc-snackbar--open {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
}

.mdc-snackbar__surface {
    background-color: #323232;
    color: #ffffff;
    border-radius: 4px;
    box-shadow: 0 4px 8px var(--shadow-medium);
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 48px;
    padding: 0 16px;
    position: relative;
    overflow: hidden;
}

.mdc-snackbar__label {
    flex: 1;
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    padding: 14px 0;
    color: #ffffff;
}

.mdc-snackbar__actions {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: 16px;
}

/* Snackbar Action Button */
.mdc-snackbar__action {
    background-color: transparent;
    color: var(--primary-light);
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    font-weight: 500;
    text-transform: uppercase;
    cursor: pointer;
    transition: background-color 0.2s ease;
    position: relative;
    overflow: hidden;
}

.mdc-snackbar__action:hover {
    background-color: rgba(66, 165, 245, 0.12);
}

.mdc-snackbar__action:focus {
    background-color: rgba(66, 165, 245, 0.16);
    outline: none;
}

.mdc-snackbar__action:active {
    background-color: rgba(66, 165, 245, 0.24);
}

/* Snackbar Dismiss Button */
.mdc-snackbar__dismiss {
    background-color: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.2s ease, color 0.2s ease;
    font-size: 18px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mdc-snackbar__dismiss:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
}

.mdc-snackbar__dismiss:focus {
    background-color: rgba(255, 255, 255, 0.12);
    color: rgba(255, 255, 255, 0.9);
    outline: none;
}

.mdc-snackbar__dismiss:active {
    background-color: rgba(255, 255, 255, 0.16);
}

/* Mobile Snackbar Adjustments */
@media (max-width: 768px) {
    .mdc-snackbar {
        left: 8px;
        right: 8px;
        transform: none;
        margin: 8px;
        min-width: auto;
        max-width: none;
    }

    .mdc-snackbar--open {
        transform: translateY(0);
    }

    .mdc-snackbar__surface {
        padding: 0 12px;
    }

    .mdc-snackbar__actions {
        margin-left: 12px;
    }
}

/* ========================================
   DARK MODE SUPPORT FOR BUTTONS & SNACKBAR
   ======================================== */

/* Button Dark Mode */
[data-theme="dark"] .mdc-button {
    color: var(--primary-light);
}

[data-theme="dark"] .mdc-button:hover {
    background-color: var(--primary-alpha-8);
}

[data-theme="dark"] .mdc-button:focus {
    background-color: var(--primary-alpha-12);
}

[data-theme="dark"] .mdc-button:active {
    background-color: var(--primary-alpha-16);
}

[data-theme="dark"] .mdc-button--outlined {
    border-color: var(--primary-light);
    color: var(--primary-light);
}

[data-theme="dark"] .mdc-button--outlined:hover {
    background-color: var(--primary-alpha-8);
    border-color: var(--primary-light);
}

[data-theme="dark"] .mdc-button--outlined:focus {
    background-color: var(--primary-alpha-12);
    border-color: var(--primary-light);
}

[data-theme="dark"] .mdc-button--outlined:active {
    background-color: var(--primary-alpha-16);
    border-color: var(--primary-color);
}

[data-theme="dark"] .mdc-button--raised {
    background-color: var(--primary-color);
    color: var(--text-on-primary);
}

[data-theme="dark"] .mdc-button--raised:hover {
    background-color: var(--primary-light);
}

[data-theme="dark"] .mdc-button--raised:focus {
    background-color: var(--primary-light);
}

[data-theme="dark"] .mdc-button--raised:active {
    background-color: var(--primary-dark);
}

/* Snackbar Dark Mode */
[data-theme="dark"] .mdc-snackbar__surface {
    background-color: #424242;
    color: #ffffff;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .mdc-snackbar__action {
    color: var(--primary-light);
}

[data-theme="dark"] .mdc-snackbar__action:hover {
    background-color: rgba(66, 165, 245, 0.12);
}

[data-theme="dark"] .mdc-snackbar__action:focus {
    background-color: rgba(66, 165, 245, 0.16);
}

[data-theme="dark"] .mdc-snackbar__action:active {
    background-color: rgba(66, 165, 245, 0.24);
}

/* ========================================
   ADDITIONAL BUTTON VARIANTS FOR FUTURE USE
   ======================================== */

/* Success Button */

.mdc-button:not(.mdc-button--raised) .mdc-button__label {
    color: var(--primary-color) !important
}

.mdc-button.mdc-color-button .mdc-button__label {
    color: var(--text-on-primary) !important
}


.mdc-button--raised:not(:disabled) {
    background-color:var(--primary-color);
    color: var(--text-on-primary) !important;
}

.mdc-button--success {
    background-color: var(--success-color);
}

.mdc-button--success:hover {
    background-color: #66bb6a;
}

.mdc-button--success:focus {
    background-color: #66bb6a;
}

.mdc-button--success:active {
    background-color: #388e3c;
}

/* Warning Button */
.mdc-button--warning {
    background-color: var(--warning-color);
}

.mdc-button--warning:hover {
    background-color: #ffb74d;
}

.mdc-button--warning:focus {
    background-color: #ffb74d;
}

.mdc-button--warning:active {
    background-color: #f57c00;
}

/* Error/Danger Button */
.mdc-button--error {
    background-color: var(--error-color);
}

.mdc-button--error:hover {
    background-color: var(--error-color);
}

.mdc-button--error:focus {
    background-color: var(--error-color);
}

.mdc-button--error:active {
    background-color: var(--error-color);
}

/* Info Button */
.mdc-button--info {
    background-color: var(--info-color);
    color: var(--text-on-primary);
}

.mdc-button--info:hover {
    background-color: #64b5f6;
}

.mdc-button--info:focus {
    background-color: #64b5f6;
}

.mdc-button--info:active {
    background-color: #1976d2;
}

/* Secondary Button */
.mdc-button--secondary {
    background-color: var(--secondary-color);
    color: var(--text-primary);
}

.mdc-button--secondary:hover {
    background-color: var(--secondary-light);
}

.mdc-button--secondary:focus {
    background-color: var(--secondary-light);
}

.mdc-button--secondary:active {
    background-color: var(--secondary-dark);
}

/* Small Button */
.mdc-button--small {
    height: 32px;
    padding: 0 12px;
    font-size: 13px;
}

/* Large Button */
.mdc-button--large {
    height: 48px;
    padding: 0 24px;
    font-size: 16px;
}

/* Full Width Button */
.mdc-button--full-width {
    width: 100%;
    justify-content: center;
}

/* Compact Button (for tight spaces) */
.mdc-button--compact {
    height: 28px;
    padding: 0 8px;
    font-size: 12px;
    min-width: 48px;
}

/* Sidebar Styles */
.sidebar {
    width: 280px;
    background: var(--background-surface);
    border-right: 1px solid var(--border-light);
    margin-top: 56px;
    height: calc(100vh - 56px);
    overflow-y: auto;
    position: fixed;
    left: 0;
    z-index: 1000;
    transition: transform 0.3s ease;
}

.sidebar.collapsed {
    transform: translateX(-100%);
}

.sidebar-section {
    padding: 5px 0;
}

.sidebar-section:not(:first-child) {
    border-top: 1px solid var(--border-light);
    padding: 10px 0 16px 0;
    margin-top: 16px;
    position: relative;
}

.sidebar-section:not(:last-child) {
    /* padding-bottom: 20px; */
    margin-bottom: 8px;
}

.sidebar-section-title {
    position: absolute;
    top: -9px;
    left: 20px;
    background: var(--background-surface);
    padding: 0 12px;
    font-size: 10px;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    z-index: 1;
}

/* Dark Mode Support for Sidebar Section Titles */
[data-theme="dark"] .sidebar-section-title {
    background: var(--background-surface);
    color: var(--text-secondary);
}

.sidebar-item {
    display: flex;
    align-items: center;
    padding: 10px 20px;
    cursor: pointer;
    transition: background-color 0.2s;
    color: var(--text-secondary);
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.sidebar-item:hover {
    background-color: var(--surface-hover);
}

.sidebar-item.active {
    background-color: var(--surface-selected);
    color: var(--primary-color);
    border-right: 3px solid var(--primary-color);
}

.sidebar-item .material-icons {
    margin-right: 16px;
    font-size: 20px;
}

.sidebar-item span:not(.material-icons) {
    font-size: 14px;
    font-weight: 400;
    flex: 1;
}

.sidebar-item .expand-icon {
    margin-left: auto;
    margin-right: 0;
    transition: transform 0.2s;
    font-size: 18px;
}

.sidebar-item.expanded .expand-icon {
    transform: rotate(180deg);
}

/* Submenu Styles */
.submenu {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background-color: var(--background-secondary);
    position: relative;
}

.submenu.expanded {
    max-height: 400px;
}

.submenu-item {
    display: flex;
    align-items: center;
    padding: 10px 24px 10px 56px;
    cursor: pointer;
    transition: background-color 0.2s;
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 14px;
    position: relative;
    overflow: hidden;
}

.submenu-item:hover {
    background-color: var(--surface-hover);
}

.submenu-item.active {
    background-color: var(--surface-selected);
    color: var(--primary-color);
}

/* Visual hierarchy line for level 1 submenu items */
.submenu-item::before {
    content: '';
    position: absolute;
    left: 32px;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: var(--border-light);
    opacity: 0.6;
}

/* Horizontal connector line for submenu items */
.submenu-item::after {
    content: '';
    position: absolute;
    left: 32px;
    top: 50%;
    width: 16px;
    height: 2px;
    background-color: var(--border-light);
    opacity: 0.6;
    transform: translateY(-50%);
}

/* Two-level submenu styles */
.submenu-item-with-submenu {
    justify-content: space-between;
}

.submenu-item-with-submenu .expand-icon {
    margin-left: auto;
    transition: transform 0.2s;
    font-size: 16px;
    color: var(--text-secondary);
}

.submenu-item-with-submenu.expanded .expand-icon {
    transform: rotate(180deg);
}

/* Submenu icons styling */
.submenu-icon {
    font-size: 16px !important;
    margin-right: 12px;
    color: var(--text-secondary);
    flex-shrink: 0;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.submenu-item:hover .submenu-icon {
    color: var(--text-primary);
}

.submenu-item.active .submenu-icon {
    color: var(--primary-color);
}

/* Level 2 submenu container */
.submenu-level-2 {
    background-color: var(--background-tertiary);
    border-left: 2px solid var(--primary-alpha-24);
    margin-left: 24px;
}

.submenu-level-2.expanded {
    max-height: 300px;
}

/* Level 2 submenu items */
.submenu-item-level-2 {
    padding: 8px 24px 8px 48px;
    font-size: 13px;
    color: var(--text-secondary);
    position: relative;
}

.submenu-item-level-2:hover {
    background-color: var(--surface-hover);
    color: var(--text-primary);
}

.submenu-item-level-2.active {
    background-color: var(--surface-selected);
    color: var(--primary-color);
    font-weight: 500;
}

/* Visual hierarchy line for level 2 submenu items */
.submenu-item-level-2::before {
    content: '';
    position: absolute;
    left: 24px;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: var(--primary-alpha-24);
    opacity: 0.8;
}

/* Horizontal connector line for level 2 submenu items */
.submenu-item-level-2::after {
    content: '';
    position: absolute;
    left: 24px;
    top: 50%;
    width: 12px;
    height: 2px;
    background-color: var(--primary-alpha-24);
    opacity: 0.8;
    transform: translateY(-50%);
}

/* Sidebar Search Styles */
.sidebar-search {
    z-index: 1001;
    background: var(--background-surface);
    padding: 8px 0;
    text-align: center;
    /* border-top: 1px solid var(--border-light); */
}

.sidebar-search .mdc-text-field {
    width: 90%;
    height: 40px;
}

.sidebar-search .mdc-text-field__input {
    font-size: 14px;
    padding: 8px 12px;
    color: var(--text-primary);
}

.sidebar-search .mdc-text-field__input::placeholder {
    color: var(--text-secondary);
    opacity: 0.7;
}

.sidebar-search .mdc-text-field--filled {
    background-color: var(--surface-hover);
    border-radius: 8px;
}

.sidebar-search .mdc-text-field--filled:not(.mdc-text-field--disabled) {
    background-color: var(--surface-hover);
}

.sidebar-search .mdc-text-field--filled:not(.mdc-text-field--disabled):hover {
    background-color: var(--surface-selected);
}

.sidebar-search .mdc-text-field--filled.mdc-text-field--focused {
    background-color: var(--surface-selected);
}

.sidebar-search .mdc-text-field__ripple {
    background-color: transparent;
}

.sidebar-search .mdc-line-ripple {
    display: none;
}

/* Search expanded submenu styles */
.submenu.search-expanded {
    max-height: none !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Main Content Styles */
.main-content {
    margin-left: 280px;
    margin-top: 56px;
    padding: 80px 24px 24px 24px; /* Top padding increased to account for fixed header */
    flex: 1;
    background: var(--background-primary);
    min-height: calc(100vh - 56px);
    transition: margin-left 0.3s ease;
    overflow-x: hidden; /* Prevent horizontal page scrolling */
    max-width: 100vw; /* Ensure content doesn't exceed viewport width */
    box-sizing: border-box;
}

.main-content.expanded {
    margin-left: 0;
}

/* Content header adjustments when sidebar is collapsed */
.content-header.expanded {
    left: 0;
}

.content-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    position: fixed;
    top: 56px; /* Below the app bar */
    left: 280px; /* Account for sidebar width */
    right: 0;
    background: var(--background-surface);
    padding: 8px 24px;
    z-index: 100;
    border-bottom: 1px solid var(--border-light);
    transition: left 0.3s ease;
}

.content-header .material-icons:first-child {
    margin-right: 8px;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.2s;
    position: relative;
    overflow: hidden;
}

.content-header .material-icons:first-child:hover {
    background-color: var(--surface-hover);
}

.page-title {
    font-size: 24px;
    font-weight: 500;
    color: var(--text-primary);
    margin: 0;
    flex: 1;
    display: flex;
    align-items: center;
    gap: 8px;
}

.page-title-count {
    font-size: 14px;
    font-weight: 400;
    color: var(--text-secondary);
    background: var(--surface-variant);
    padding: 1px 6px; /* Reduced padding */
    border-radius: 12px;
}

.content-header .actions {
    margin-left: auto;
    display: flex;
    align-items: center;
    gap: 8px;
}

.content-header .actions .material-icons {
    margin: 0;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.2s;
    position: relative;
    overflow: hidden;
}

.content-header .actions .material-icons:hover {
    background-color: var(--surface-hover);
}

.add-btn {
    background-color: var(--primary-color) !important;
    color: var(--text-on-primary) !important;
    margin-right: 16px;
}

.add-btn:hover {
    background-color: var(--primary-light) !important;
}

/* Quick Filter Chips */
.quick-filter-chips {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 16px;
    padding: 0;
}

/* Mobile: Force horizontal scrolling */
@media (max-width: 768px) {
    .quick-filter-chips {
        flex-wrap: nowrap;
        overflow-x: auto;
        overflow-y: hidden;
    }
}

.quick-filter-chip {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border: 1px solid var(--border-light);
    border-radius: 20px;
    background: var(--background-surface);
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    outline: none;
    white-space: nowrap;
}

.quick-filter-chip:hover {
    background: var(--surface-hover);
    border-color: var(--border-medium);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px var(--shadow-light);
}

.quick-filter-chip.active {
    background: var(--primary-color);
    color: var(--text-on-primary);
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px var(--primary-alpha-24);
}

.quick-filter-chip.active:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
}

.quick-filter-chip .material-icons {
    font-size: 18px;
}

.quick-filter-chip.active .material-icons {
    color: var(--text-on-primary);
}

/* Desktop specific styling */
@media (min-width: 769px) {
    .quick-filter-chip {
        min-width: 100px;
        max-width: 140px;
    }

    .quick-filter-chip span:last-child {
        white-space: nowrap;
    }
}

/* No Data Found Component */
.no-data-found {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    padding: 40px 20px;
}

.no-data-content {
    text-align: center;
    max-width: 400px;
}

.no-data-icon {
    font-size: 64px;
    color: var(--text-disabled);
    margin-bottom: 16px;
    display: block;
}

.no-data-title {
    font-size: 20px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.no-data-message {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 24px;
    line-height: 1.5;
}

.no-data-action {
    margin-top: 8px;
}

/* Mobile responsive styling for no data component */
@media (max-width: 768px) {
    .no-data-found {
        min-height: 300px;
        padding: 32px 20px;
    }

    .no-data-found .no-data-icon {
        font-size: 48px;
        margin-bottom: 12px;
    }

    .no-data-found .no-data-title {
        font-size: 18px;
        margin-bottom: 6px;
    }

    .no-data-found .no-data-message {
        font-size: 13px;
        margin-bottom: 20px;
    }
}

/* ========================================
   LOGIN PAGE STYLES
   ======================================== */

/* Login page body override */
body.login-page {
    background: var(--background-primary);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow-x: hidden;
}

/* Background Pattern */
.background-pattern {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(25, 118, 210, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(25, 118, 210, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(25, 118, 210, 0.02) 0%, transparent 50%);
    pointer-events: none;
}

/* Login Container */
.login-container {
    position: relative;
    width: 100%;
    max-width: 1100px;
    padding: 16px;
    z-index: 1;
}

/* Login Card */
.login-card {
    background: var(--background-surface);
    border-radius: 24px;
    overflow: hidden;
    box-shadow: 0 20px 60px var(--shadow-light);
    border: 1px solid var(--border-light);
    display: grid;
    grid-template-columns: 1fr 1fr;
    height: 85vh;
    max-height: 700px;
    min-height: 500px;
}

/* Left Column - App Information */
.login-info-section {
    background: var(--primary-gradient);
    color: var(--text-on-primary);
    padding: 40px 36px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.login-info-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.08)"/><circle cx="10" cy="60" r="0.8" fill="rgba(255,255,255,0.06)"/><circle cx="90" cy="30" r="0.6" fill="rgba(255,255,255,0.04)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.info-content {
    position: relative;
    z-index: 1;
}

.app-branding {
    margin-bottom: 32px;
}

.brand-logo {
    width: 64px;
    height: 64px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    backdrop-filter: blur(10px);
}

.brand-logo .material-icons {
    font-size: 32px;
    color: var(--text-on-primary);
}

.brand-title {
    font-size: 26px;
    font-weight: 600;
    margin-bottom: 8px;
    line-height: 1.2;
}

.brand-subtitle {
    font-size: 16px;
    opacity: 0.9;
    font-weight: 300;
}

/* Feature Highlights */
.feature-highlights {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 6px;
}

.feature-illustration {
    width: 80px;
    height: 60px;
    flex-shrink: 0;
    margin-bottom: 8px;
}

.feature-illustration svg {
    width: 100%;
    height: 100%;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.feature-text h3 {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 2px;
}

.feature-text p {
    font-size: 12px;
    opacity: 0.8;
    line-height: 1.3;
}

/* Right Column - Login Form */
.login-form-section {
    padding: 40px 36px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.form-content {
    max-width: 380px;
    margin: 0 auto;
    width: 100%;
}

.form-header {
    text-align: center;
    margin-bottom: 32px;
}

.form-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 6px;
}

.form-subtitle {
    font-size: 14px;
    color: var(--text-secondary);
    font-weight: 400;
}

/* Login Form Styles */
.login-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-field {
    width: 100%;
}

/* Ensure all form inputs have consistent width */
.login-form .mdc-text-field,
.login-form .mdc-select {
    width: 100% !important;
}

.login-form .mdc-text-field__input {
    width: 100%;
}

.login-form .mdc-select__anchor {
    width: 100%;
}

.checkbox-field {
    margin: 8px 0;
}

.login-form .mdc-form-field {
    display: flex;
    align-items: center;
    gap: 8px;
}

.login-form .mdc-form-field label {
    font-size: 14px;
    color: var(--text-secondary);
    cursor: pointer;
}

/* Login Button */
.login-button {
    width: 100%;
    height: 52px;
    background: var(--primary-gradient) !important;
    color: var(--text-on-primary) !important;
    font-weight: 500;
    text-transform: none;
    font-size: 16px;
    border-radius: 12px;
    box-shadow: 0 4px 12px var(--primary-alpha-24);
    transition: all 0.3s ease;
}

.login-button:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-light)) !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px var(--primary-alpha-24);
}

.login-button .mdc-button__icon {
    margin-right: 8px;
}

/* Forgot Password Link */
.forgot-password-link {
    display: block;
    text-align: center;
    color: var(--primary-color);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: color 0.2s ease;
}

.forgot-password-link:hover {
    color: var(--primary-light);
    text-decoration: underline;
}

/* Form Footer */
.form-footer {
    text-align: center;
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid var(--border-light);
    color: var(--text-disabled);
    font-size: 11px;
}

/* Login Page Material Design Customizations */
.login-form .mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,
.login-form .mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,
.login-form .mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing {
    border-color: var(--border-light);
}

.login-form .mdc-text-field--outlined:not(.mdc-text-field--disabled):hover .mdc-notched-outline__leading,
.login-form .mdc-text-field--outlined:not(.mdc-text-field--disabled):hover .mdc-notched-outline__notch,
.login-form .mdc-text-field--outlined:not(.mdc-text-field--disabled):hover .mdc-notched-outline__trailing {
    border-color: var(--primary-color);
}

.login-form .mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,
.login-form .mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,
.login-form .mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing {
    border-color: var(--primary-color);
    border-width: 2px;
}

.login-form .mdc-text-field--focused .mdc-floating-label {
    color: var(--primary-color);
}

.login-form .mdc-select:not(.mdc-select--disabled) .mdc-notched-outline__leading,
.login-form .mdc-select:not(.mdc-select--disabled) .mdc-notched-outline__notch,
.login-form .mdc-select:not(.mdc-select--disabled) .mdc-notched-outline__trailing {
    border-color: var(--border-light);
}

.login-form .mdc-select:not(.mdc-select--disabled):hover .mdc-notched-outline__leading,
.login-form .mdc-select:not(.mdc-select--disabled):hover .mdc-notched-outline__notch,
.login-form .mdc-select:not(.mdc-select--disabled):hover .mdc-notched-outline__trailing {
    border-color: var(--primary-color);
}

.login-form .mdc-select--focused .mdc-notched-outline__leading,
.login-form .mdc-select--focused .mdc-notched-outline__notch,
.login-form .mdc-select--focused .mdc-notched-outline__trailing {
    border-color: var(--primary-color);
    border-width: 2px;
}

.login-form .mdc-select--focused .mdc-floating-label {
    color: var(--primary-color);
}

/* Login Page Dropdown Z-index */
.login-form .mdc-select__menu {
    z-index: 9999;
}

.login-form .mdc-menu-surface {
    z-index: 9999;
}

.login-form .mdc-select__menu .mdc-menu-surface {
    z-index: 9999;
}

.login-form .mdc-select {
    z-index: 100;
}

.login-form .mdc-select--focused {
    z-index: 200;
}

/* Ensure select field renders properly */
.login-form .mdc-select {
    display: block !important;
    position: relative;
    visibility: visible !important;
    opacity: 1 !important;
}

.login-form .mdc-select__anchor {
    display: flex !important;
    position: relative;
    visibility: visible !important;
}

/* Force the first form field (school year) to be visible */
.login-form .form-field:first-child {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
}

/* Login Page Checkbox Customization */
.login-form .mdc-checkbox__native-control:enabled:checked ~ .mdc-checkbox__background,
.login-form .mdc-checkbox__native-control:enabled:indeterminate ~ .mdc-checkbox__background {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Login Page Responsive Design */
@media (max-width: 1024px) {
    .login-container {
        max-width: 900px;
    }

    .login-info-section,
    .login-form-section {
        padding: 48px 32px;
    }

    .brand-title {
        font-size: 28px;
    }

    .form-title {
        font-size: 24px;
    }
}

@media (max-width: 900px) {
    .login-card {
        grid-template-columns: 1fr;
        min-height: auto;
    }

    .login-info-section {
        display: none;
    }

    .login-form-section {
        padding: 60px 40px;
    }

    .form-content {
        max-width: 100%;
    }
}

@media (max-width: 480px) {
    .login-container {
        padding: 16px;
    }

    .login-form-section {
        padding: 40px 24px;
    }

    .form-title {
        font-size: 22px;
    }

    .form-header {
        margin-bottom: 32px;
    }

    .login-form {
        gap: 20px;
    }
}

/* Login Form Field Ordering */
.login-form .form-field {
    order: initial;
}

/* Explicitly set order for each form field */
.login-form .form-field:nth-child(1) { order: 1; }  /* School Year */
.login-form .form-field:nth-child(2) { order: 2; }  /* Username */
.login-form .form-field:nth-child(3) { order: 3; }  /* Password */
.login-form .form-field:nth-child(4) { order: 4; }  /* Remember Me */
.login-form .form-field:nth-child(5) { order: 5; }  /* Login Button */
.login-form .form-field:nth-child(6) { order: 6; }  /* Forgot Password */

/* Animation for login form fields */
.login-form .form-field {
    animation: slideInUp 0.3s ease forwards;
    opacity: 0;
    transform: translateY(20px);
    animation-fill-mode: forwards;
}

.login-form .form-field:nth-child(1) { animation-delay: 0.1s; }
.login-form .form-field:nth-child(2) { animation-delay: 0.2s; }
.login-form .form-field:nth-child(3) { animation-delay: 0.3s; }
.login-form .form-field:nth-child(4) { animation-delay: 0.4s; }
.login-form .form-field:nth-child(5) { animation-delay: 0.5s; }
.login-form .form-field:nth-child(6) { animation-delay: 0.6s; }

@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Filter Chips */
.filter-chips-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 16px;
    min-height: 0;
    transition: all 0.3s ease;
}

.filter-chips-container:empty {
    margin-bottom: 0;
}

.filter-chip {
    display: inline-flex;
    align-items: center;
    background: var(--surface-selected);
    color: var(--primary-color);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    gap: 4px;
    animation: chipFadeIn 0.3s ease;
    height: 24px;
}

.filter-chip-remove {
    background: transparent;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    padding: 1px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
    font-size: 14px;
    width: 16px;
    height: 16px;
}

.filter-chip-remove:hover {
    background-color: var(--primary-alpha-8);
}

@keyframes chipFadeIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Student List Styles */
.students-list {
    position: relative;
}

.student-item {
    display: flex;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s ease, transform 0.2s ease;
    position: relative;
    overflow: hidden;
    opacity: 1;
    transform: translateY(0);
}

.student-item:hover {
    background-color: var(--background-secondary);
}

.student-item:last-child {
    border-bottom: none;
}

.student-photo {
    width: 96px;
    height: 96px;
    border-radius: 50%;
    margin-right: 2px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border: 2px solid var(--border-light);
}

.student-info {
    flex: 1;
    margin-left: 12px;
    margin-right: 8px;
    width: 100%;
    min-width: 0; /* Allow flex item to shrink below content size */
}

.student-header {
    width: 100%;
    padding-right: 80px; /* Leave space for absolutely positioned student ID */
}

.student-name {
    font-size: 15px;
    font-weight: 500;
    color: var(--text-primary);
    line-height: 1.2;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.student-id {
    position: absolute;
    top: 16px;
    right: 8px;
    font-size: 12px;
    font-weight: 500;
    color: var(--text-secondary);
    white-space: nowrap;
    z-index: 1;
}

.student-name-ar {
    font-size: 13px;
    font-weight: 400;
    color: var(--text-primary);
    margin-bottom: 4px;
    line-height: 1.3;
    text-align: left;
}

.student-birth-date {
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 4px;
    line-height: 1.2;
}

.student-grades {
    font-size: 12px;
    line-height: 1.2;
    display: flex;
    align-items: center;
    gap: 8px;
}

.grade-fr,
.grade-ar {
    background: var(--surface-hover);
    color: var(--text-secondary);
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
}

.student-grades .grade-separator {
    color: var(--text-secondary);
    font-weight: normal;
}

/* Legacy styles for compatibility */
.student-details {
    font-size: 13px;
    color: var(--text-secondary);
    line-height: 1.4;
}

.student-level {
    display: inline-block;
    background: var(--surface-selected);
    color: var(--primary-color);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    margin-right: 8px;
}

.student-actions {
    position: absolute;
    bottom: 8px;
    right: 8px;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 4px;
}

.edit-btn, .delete-btn {
    padding: 6px;
    border-radius: 50%;
    background: var(--background-surface);
    border: 1px solid var(--border-light);
    cursor: pointer;
    transition: all 0.2s;
    color: var(--text-secondary);
    position: relative;
    overflow: hidden;
    box-shadow: 0 1px 3px var(--shadow-light);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.edit-btn .material-icons,
.delete-btn .material-icons {
    font-size: 16px;
}

.edit-btn:hover {
    background-color: var(--primary-color);
    color: var(--text-on-primary);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: 0 2px 6px var(--shadow-medium);
}

.delete-btn {
    color: var(--error-color);
}

.delete-btn:hover {
    background-color: var(--error-color);
    color: var(--text-on-primary);
    border-color: var(--error-color);
    transform: translateY(-1px);
    box-shadow: 0 2px 6px var(--shadow-medium);
}

/* Ripple Effect Styles */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
    /* Ensure ripples stay within their container */
    max-width: 200px;
    max-height: 200px;
    z-index: 1;
}

.ripple-dark {
    background: rgba(0, 0, 0, 0.3);
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Student Detail Page Styles */
.student-detail-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--background-surface);
    z-index: 2500;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.student-detail-overlay.active {
    opacity: 1;
    visibility: visible;
}

.student-detail-page {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: var(--background-surface);
}

.student-detail-header {
    display: flex;
    align-items: center;
    padding: 16px;
    background: var(--primary-gradient);
    color: var(--text-on-primary);
    box-shadow: 0 2px 4px var(--shadow-medium);
    position: sticky;
    top: 0;
    z-index: 10;
}

.student-detail-back {
    background: transparent;
    border: none;
    color: var(--text-on-primary);
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    margin-right: 8px;
    transition: background-color 0.2s;
    position: relative;
    overflow: hidden;
}

.student-detail-back:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.student-detail-title {
    flex: 1;
    font-size: 20px;
    font-weight: 500;
}

.student-detail-more {
    background: transparent;
    border: none;
    color: var(--text-on-primary);
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.2s;
    position: relative;
    overflow: hidden;
}

.student-detail-more:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.student-detail-content {
    flex: 1;
    overflow-y: auto;
    padding: 0 0 24px 0;
}

/* Student Profile Section */
.student-profile-section {
    display: flex;
    align-items: center;
    padding: 24px 16px;
    background: var(--background-surface);
    border-bottom: 1px solid var(--border-light);
}

.student-profile-photo {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-size: cover;
    background-position: center;
    margin-right: 16px;
    border: 3px solid var(--primary-color);
}

.student-profile-info {
    flex: 1;
}

.student-profile-name {
    font-size: 20px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.student-profile-name-ar {
    font-size: 16px;
    color: var(--text-secondary);
    margin-bottom: 4px;
    text-align: left;
}

.student-profile-id {
    font-size: 14px;
    color: var(--text-secondary);
    background: var(--surface-selected);
    padding: 4px 8px;
    border-radius: 12px;
    display: inline-block;
}

/* Detail Sections */
.detail-section {
    margin-bottom: 8px;
    background: var(--background-surface);
    border-bottom: 1px solid var(--border-light);
}

.detail-section-header {
    display: flex;
    align-items: center;
    padding: 16px;
    background: var(--background-secondary);
}

.detail-section-header .material-icons {
    color: var(--primary-color);
    margin-right: 12px;
    font-size: 20px;
}

.detail-section-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-primary);
}

.detail-section-content {
    padding: 0 16px 16px 16px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid var(--border-light);
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    font-size: 14px;
    color: var(--text-secondary);
    font-weight: 400;
}

.detail-value {
    font-size: 14px;
    color: var(--text-primary);
    font-weight: 500;
    text-align: right;
}

/* Results Section */
.results-tabs {
    display: flex;
    margin-bottom: 16px;
    background: var(--background-secondary);
    border-radius: 8px;
    padding: 4px;
}

.results-tab {
    flex: 1;
    padding: 8px 16px;
    background: transparent;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-secondary);
    transition: all 0.2s ease;
}

.results-tab.active {
    background: var(--primary-color);
    color: var(--text-on-primary);
}

.results-content {
    position: relative;
}

.results-tab-content {
    display: none;
}

.results-tab-content.active {
    display: block;
}

.result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid var(--border-light);
}

.result-item:last-child {
    border-bottom: none;
}

.result-subject {
    font-size: 14px;
    color: var(--text-primary);
}

.result-grade {
    font-size: 14px;
    font-weight: 500;
    color: var(--primary-color);
    background: var(--surface-selected);
    padding: 4px 8px;
    border-radius: 12px;
}

/* Attendance Section */
.attendance-summary {
    display: flex;
    justify-content: space-around;
    margin-bottom: 24px;
    padding: 16px;
    background: var(--background-secondary);
    border-radius: 12px;
}

.attendance-stat {
    text-align: center;
}

.attendance-number {
    font-size: 24px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 4px;
}

.attendance-label {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
}

.attendance-calendar {
    background: var(--background-secondary);
    border-radius: 12px;
    padding: 16px;
}

.calendar-header {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 12px;
}

.calendar-days {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.calendar-day {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
}

.calendar-day.present {
    background: var(--success-color);
    color: white;
}

.calendar-day.absent {
    background: var(--error-color);
    color: white;
}

.calendar-day.late {
    background: var(--warning-color);
    color: white;
}

/* Student Detail Page Responsive Styles */
@media (min-width: 768px) {
    .mdc-tab-bar {
        width: fit-content;
    }

    .student-detail-page {
        max-width: 800px;
        margin: 0 auto;
        box-shadow: 0 0 24px var(--shadow-light);
    }

    .student-profile-section {
        padding: 32px 24px;
    }

    .student-profile-photo {
        width: 100px;
        height: 100px;
        margin-right: 24px;
    }

    .student-profile-name {
        font-size: 24px;
    }

    .student-profile-name-ar {
        font-size: 18px;
    }

    .detail-section-header {
        padding: 20px 24px;
    }

    .detail-section-content {
        padding: 0 24px 20px 24px;
    }

    .detail-item {
        padding: 16px 0;
    }

    .detail-label {
        font-size: 15px;
    }

    .detail-value {
        font-size: 15px;
    }

    .attendance-summary {
        padding: 24px;
    }

    .attendance-number {
        font-size: 28px;
    }

    .attendance-label {
        font-size: 14px;
    }

    .calendar-days {
        gap: 12px;
    }

    .calendar-day {
        width: 40px;
        height: 40px;
        font-size: 14px;
    }
}

/* Dark Mode Support for Student Detail Page */
[data-theme="dark"] .bottom-sheet {
    background: var(--background-surface);
    color: var(--text-primary);
}

[data-theme="dark"] .bottom-sheet-title {
    color: var(--text-primary);
}

[data-theme="dark"] .bottom-sheet-close {
    color: var(--text-secondary);
}

[data-theme="dark"] .bottom-sheet-close:hover {
    background-color: var(--surface-hover);
}

[data-theme="dark"] .student-preview .student-details {
    color: var(--text-secondary);
}

/* Bottom Sheet Styles */
.bottom-sheet-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.bottom-sheet-overlay.active {
    opacity: 1;
    visibility: visible;
}

.bottom-sheet {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-radius: 16px 16px 0 0;
    box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.2);
    transform: translateY(100%);
    transition: transform 0.3s ease;
    z-index: 2001;
    max-height: 60vh;
    overflow-y: auto;
}

.bottom-sheet.active {
    transform: translateY(0);
}

.bottom-sheet-header {
    padding: 16px 24px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.bottom-sheet-handle {
    width: 32px;
    height: 4px;
    background: #e0e0e0;
    border-radius: 2px;
    margin: 8px auto 16px;
}

.bottom-sheet-title {
    font-size: 18px;
    font-weight: 500;
    color: #212121;
    flex: 1;
}

.bottom-sheet-close {
    padding: 8px;
    border-radius: 50%;
    background: transparent;
    border: none;
    cursor: pointer;
    color: #757575;
    position: relative;
    overflow: hidden;
    transition: background-color 0.2s;
}

.bottom-sheet-close:hover {
    background-color: #f5f5f5;
}

.bottom-sheet-content {
    padding: 0 24px 24px;
}

.student-preview {
    display: flex;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 16px;
}

.student-preview .student-photo {
    width: 48px;
    height: 48px;
    margin-right: 12px;
}

.student-preview .student-info {
    flex: 1;
}

.student-preview .student-name {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 2px;
}

.student-preview .student-details {
    font-size: 14px;
    color: #757575;
}

.bottom-sheet-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.bottom-sheet-action {
    display: flex;
    align-items: center;
    padding: 16px 0;
    cursor: pointer;
    border-radius: 8px;
    transition: background-color 0.2s;
    position: relative;
    overflow: hidden;
    text-decoration: none;
    color: inherit;
}

.bottom-sheet-action:hover {
    background-color: #f5f5f5;
}

.bottom-sheet-action .material-icons {
    margin-right: 16px;
    font-size: 24px;
    color: #757575;
    width: 24px;
    text-align: center;
}

.bottom-sheet-action.delete .material-icons {
    color: #f44336;
}

.bottom-sheet-action-content {
    flex: 1;
}

.bottom-sheet-action-title {
    font-size: 16px;
    font-weight: 400;
    color: #212121;
    margin-bottom: 2px;
}

.bottom-sheet-action.delete .bottom-sheet-action-title {
    color: #f44336;
}

.bottom-sheet-action-subtitle {
    font-size: 14px;
    color: #757575;
}

/* Data Table Container */
.data-table-container {
    width: 100%;
    position: relative;
    z-index: 1;
    overflow: hidden; /* Prevent container from expanding beyond viewport */
}

/* Targeted Loading Overlay */
.target-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--background-overlay);
    backdrop-filter: blur(2px);
    z-index: 100;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    border-radius: 8px;
}

.target-loading-overlay.active {
    opacity: 1;
    visibility: visible;
}

.target-loading-content {
    text-align: center;
    background: var(--background-surface);
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 4px 20px var(--shadow-light);
    min-width: 160px;
}

.target-loading-text {
    margin-top: 12px;
    font-size: 14px;
    font-weight: 400;
    color: var(--text-secondary);
}

/* Simple Spinner Styles */
.simple-spinner {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

.spinner-svg {
    width: 40px;
    height: 40px;
    animation: spin 2s linear infinite;
}

.spinner-circle {
    stroke: var(--primary-color);
    stroke-width: 3;
    fill: none;
    stroke-linecap: round;
    stroke-dasharray: 31.416;
    stroke-dashoffset: 31.416;
    animation: dash 1.5s ease-in-out infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes dash {
    0% {
        stroke-dasharray: 1, 150;
        stroke-dashoffset: 0;
    }
    50% {
        stroke-dasharray: 90, 150;
        stroke-dashoffset: -35;
    }
    100% {
        stroke-dasharray: 90, 150;
        stroke-dashoffset: -124;
    }
}

/* Table Controls */
.table-controls {
    display: flex !important;
    align-items: center;
    justify-content: space-between;
    padding: 16px 0;
    gap: 16px;
    flex-wrap: wrap;
    position: relative;
    z-index: 10;
}

.search-container {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    max-width: 400px;
}

.search-label {
    font-size: 14px;
    color: var(--text-secondary);
    white-space: nowrap;
}

.search-container .mdc-text-field {
    min-width: 280px;
    flex: 1;
}

.table-controls .mdc-text-field {
    min-width: 280px;
    flex: 1;
    max-width: 400px;
}

.per-page-container {
    display: flex;
    align-items: center;
    gap: 12px;
    white-space: nowrap;
    position: relative;
    z-index: 50;
}

.select-container {
    position: relative;
    z-index: 50;
}

.per-page-label {
    font-size: 14px;
    color: #757575;
}

.per-page-container .mdc-select {
    /* min-width: 80px; */
    position: relative;
    z-index: 50;
}

.per-page-container .mdc-select .mdc-select__menu {
    z-index: 1400;
}

.per-page-container .mdc-menu-surface {
    z-index: 1400;
}

/* MDC Data Table Full Width */
.mdc-data-table {
    width: 100%;
}

.mdc-data-table__table {
    width: 100%;
    /* min-width: 1200px; */
    table-layout: auto;
    font-family: 'Roboto Condensed', 'Roboto', sans-serif;
    font-size: 11px;
}

.mdc-data-table__table-container {
    overflow-x: auto;
    overflow-y: visible; /* Allow dropdowns to show */
    /* Enhanced horizontal scrolling */
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    scrollbar-color: #ccc #f1f1f1;
}

/* Custom scrollbar styling for webkit browsers */
.mdc-data-table__table-container::-webkit-scrollbar {
    height: 8px;
}

.mdc-data-table__table-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.mdc-data-table__table-container::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 4px;
}

.mdc-data-table__table-container::-webkit-scrollbar-thumb:hover {
    background: #999;
}

/* Add subtle shadow to indicate scrollable content */
.mdc-data-table__table-container {
    position: relative;
}

.mdc-data-table__table-container::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 20px;
    /* background: linear-gradient(to left, rgba(0,0,0,0.1), transparent); */
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

/* Show shadow when content is scrollable */
@media (max-width: 1400px) {
    .mdc-data-table__table-container::after {
        opacity: 1;
    }
}

/* Compact table styling for desktop */
@media (min-width: 1024px) {
    .mdc-data-table__header-cell,
    .mdc-data-table__cell {
        padding: 8px 12px;
        font-size: 11px;
        line-height: 1.3;
    }

    .mdc-data-table__header-cell {
        font-weight: 500;
        white-space: nowrap;
    }

    /* Numeric columns alignment */
    .numeric-column,
    .numeric-cell {
        text-align: right;
    }

    /* Student photo sizing - keep table photos small but mobile list photos larger */
    .mdc-data-table .student-photo {
        width: 42px;
        height: 42px;
        border-radius: 50%;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        flex-shrink: 0;
    }

    /* Table action buttons compact */
    .table-actions {
        display: flex;
        gap: 2px;
        justify-content: center;
        position: relative;
        overflow: visible; /* Allow dropdown to show */
    }

    .table-actions .mdc-icon-button {
        width: 32px;
        height: 32px;
        padding: 4px;
    }

    .table-actions .material-icons {
        font-size: 18px;
    }

    /* Ensure minimum column widths for readability */
    .mdc-data-table__header-cell:nth-child(1),
    .mdc-data-table__cell:nth-child(1) {
        width: 48px; /* Checkbox column */
    }

    .mdc-data-table__header-cell:nth-child(2),
    .mdc-data-table__cell:nth-child(2) {
        width: 48px; /* Photo column */
    }

    .mdc-data-table__header-cell:nth-child(3),
    .mdc-data-table__cell:nth-child(3) {
        min-width: 150px; /* Full Name column */
    }

    .mdc-data-table__header-cell:nth-child(4),
    .mdc-data-table__cell:nth-child(4) {
        width: 100px; /* Student ID column */
    }

    .mdc-data-table__header-cell:nth-child(5),
    .mdc-data-table__cell:nth-child(5) {
        width: 70px; /* Gender column */
    }

    .mdc-data-table__header-cell:nth-child(6),
    .mdc-data-table__cell:nth-child(6) {
        width: 80px; /* Grade (FR) column */
    }

    .mdc-data-table__header-cell:nth-child(7),
    .mdc-data-table__cell:nth-child(7) {
        width: 100px; /* Grade (AR) column */
    }

    .mdc-data-table__header-cell:nth-child(8),
    .mdc-data-table__cell:nth-child(8) {
        width: 80px; /* Status column */
    }

    .mdc-data-table__header-cell:nth-child(9),
    .mdc-data-table__cell:nth-child(9) {
        width: 80px; /* Fees column */
    }

    .mdc-data-table__header-cell:nth-child(10),
    .mdc-data-table__cell:nth-child(10) {
        width: 100px; /* Amount Paid column */
    }

    .mdc-data-table__header-cell:nth-child(11),
    .mdc-data-table__cell:nth-child(11) {
        width: 90px; /* Remaining column */
    }

    .mdc-data-table__header-cell:nth-child(12),
    .mdc-data-table__cell:nth-child(12) {
        width: 120px; /* Actions column */
        position: relative; /* For dropdown positioning */
        overflow: visible; /* Allow dropdown to show outside cell */
    }
}

/* Table Action Dropdown Menu */
.table-action-menu {
    position: fixed; /* Use fixed positioning to escape table stacking context */
    background: var(--background-surface);
    border-radius: 4px;
    box-shadow: 0 4px 16px var(--shadow-medium);
    min-width: 200px;
    z-index: 9999; /* Very high z-index to appear above everything */
    opacity: 0;
    visibility: hidden;
    transform: translateY(-8px);
    transition: all 0.2s ease;
    border: 1px solid var(--border-light);
}

.table-action-menu.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Dropdown direction variants */
.table-action-menu.dropdown-above {
    transform: translateY(8px);
}

.table-action-menu.dropdown-above.active {
    transform: translateY(0);
}

.table-action-menu-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    font-size: 14px;
    color: var(--text-primary);
    position: relative;
    overflow: hidden;
}

.table-action-menu-item:hover {
    background-color: var(--surface-hover);
}

.table-action-menu-item:first-child {
    border-radius: 4px 4px 0 0;
}

.table-action-menu-item:last-child {
    border-radius: 0 0 4px 4px;
}

.table-action-menu-item.delete {
    color: var(--error-color);
}

.table-action-menu-item.delete:hover {
    background-color: rgba(244, 67, 54, 0.08);
}

.table-action-menu-item .material-icons {
    margin-right: 12px;
    font-size: 18px;
}

.table-action-menu-content {
    display: flex;
    flex-direction: column;
    flex: 1;
}

.table-action-menu-title {
    font-weight: 500;
    line-height: 1.2;
}

.table-action-menu-subtitle {
    font-size: 12px;
    color: var(--text-secondary);
    margin-top: 2px;
    line-height: 1.2;
}

/* Sortable Header Styling */
.mdc-data-table__header-cell--sortable {
    cursor: pointer;
    position: relative;
    user-select: none;
}

.mdc-data-table__header-cell--sortable:hover {
    background-color: rgba(0, 0, 0, 0.04);
}

.sort-icon {
    font-size: 16px;
    color: var(--border-medium);
    margin-left: 4px;
    vertical-align: middle;
    transition: color 0.2s;
}

.mdc-data-table__header-cell--sortable.sort-asc .sort-icon {
    color: var(--primary-color);
}

.mdc-data-table__header-cell--sortable.sort-desc .sort-icon {
    color: var(--primary-color);
}

/* Student Photo in Table */
.student-photo {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border: 2px solid #e0e0e0;
}

/* Table Actions */
.table-actions {
    display: flex;
    gap: 4px;
}

.table-actions .mdc-icon-button {
    width: 32px;
    height: 32px;
    padding: 4px;
}

.table-actions .action-btn {
    background: var(--background-surface);
    border: none;
    padding: 6px;
    cursor: pointer;
    border-radius: 50%; /* Circular for edit and delete buttons */
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
    color: var(--text-secondary);
    box-shadow: 0 2px 4px var(--shadow-light);
}

.table-actions .action-btn:hover {
    box-shadow: 0 4px 8px var(--shadow-medium);
    transform: translateY(-1px);
}

.table-actions .action-btn {
    background: var(--background-surface);
    color: var(--text-secondary);
    border: 1px solid var(--border-light);
}

.table-actions .action-btn.edit-btn:hover {
    background: var(--primary-color);
    color: var(--text-on-primary);
    border-color: var(--primary-color);
}


.table-actions .action-btn.delete-btn:hover {
    background: var(--error-color);
    color: var(--text-on-primary);
    border-color: var(--error-color);
}

/* More button specific styling - rectangular, no borders */
.table-actions .action-btn.more-btn {
    background: transparent;
    border: none; /* Rectangular for more button only */
    box-shadow: none;
}

.table-actions .action-btn.more-btn:hover {
    background-color: var(--surface-hover);
    box-shadow: none;
    transform: none;
}

/* Pagination */
.table-pagination {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 0;
    border-top: 1px solid var(--border-light);
    margin-top: 8px;
    flex-wrap: wrap;
    gap: 16px;
}

.pagination-info {
    font-size: 14px;
    color: var(--text-secondary);
    padding-left: 24px; /* Align with checkbox column */
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.pagination-pages {
    display: flex;
    gap: 4px;
}

.pagination-page {
    min-width: 32px;
    height: 32px;
    border-radius: 50%;
    background: transparent;
    border: none;
    cursor: pointer;
    font-size: 14px;
    color: var(--text-secondary);
    transition: background-color 0.2s;
    position: relative;
    overflow: hidden;
}

.pagination-page:hover {
    background-color: var(--primary-alpha-8);
}

.pagination-page.active {
    background-color: var(--primary-color);
    color: var(--text-on-primary);
}

.pagination-page.ellipsis {
    background: transparent;
    color: var(--text-disabled);
    cursor: default;
    pointer-events: none;
}

.pagination-page.ellipsis:hover {
    background: transparent;
}

.pagination-page.active:hover {
    background-color: var(--primary-dark);
}

/* Floating Action Button */
.mdc-fab {
    position: fixed;
    bottom: 24px;
    right: 24px;
    z-index: 999;
    background-color: var(--primary-color);
    color: var(--text-on-primary);
    opacity: 0.9; /* Make slightly transparent */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mdc-fab:hover {
    background-color: var(--primary-dark);
    opacity: 1; /* Full opacity on hover */
}

.mdc-fab--extended {
    border-radius: 28px;
}

/* QR Code FAB - positioned above Add Student FAB */
.qr-fab {
    bottom: 140px !important; /* Position above the add student FAB */
    width: 56px;
    height: 56px;
    border-radius: 50%;
}

.qr-fab .mdc-fab__icon {
    margin-right: 0; /* No margin since there's no label */
}

/* QR Scanner Modal Styles */
.qr-scanner-modal {
    max-width: 600px;
    width: 90vw;
}

.qr-scanner-content {
    padding: 24px;
    text-align: center;
}

.qr-scanner-message {
    padding: 32px 16px;
    text-align: center;
}

.qr-scanner-icon {
    margin-bottom: 16px;
}

.qr-scanner-icon .material-icons {
    font-size: 48px;
    color: var(--primary-color);
}

.qr-scanner-text {
    font-size: 16px;
    color: var(--text-secondary);
    line-height: 1.5;
}

.qr-scanner-result {
    padding: 32px 16px;
    text-align: center;
}

.qr-result-icon {
    margin-bottom: 16px;
}

.qr-result-icon .material-icons {
    font-size: 48px;
    color: var(--success-color);
}

.qr-result-title {
    font-size: 18px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 16px;
}

.qr-result-content {
    background: var(--background-secondary);
    border: 1px solid var(--border-light);
    border-radius: 8px;
    padding: 16px;
    font-family: 'Roboto Mono', monospace;
    font-size: 14px;
    color: var(--text-primary);
    word-break: break-all;
    max-height: 120px;
    overflow-y: auto;
}

/* QR Reader Container Customization */
#qr-reader {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px var(--shadow-light);
}

#qr-reader video {
    border-radius: 12px;
}

#qr-reader__scan_region {
    border-radius: 12px !important;
}

#qr-reader__dashboard {
    background: var(--background-surface) !important;
    border-radius: 0 0 12px 12px !important;
}

#qr-reader__dashboard_section {
    background: transparent !important;
}

#qr-reader__dashboard_section_csr {
    display: none !important;
}

#qr-reader__camera_selection {
    margin-bottom: 16px !important;
}

#qr-reader__camera_selection select {
    background: var(--background-surface) !important;
    border: 1px solid var(--border-light) !important;
    border-radius: 8px !important;
    padding: 8px 12px !important;
    color: var(--text-primary) !important;
    font-family: 'Roboto', sans-serif !important;
}

.mdc-fab__icon {
    margin-right: 8px;
}

.mdc-fab__label {
    font-size: 14px;
    font-weight: 500;
}

/* Fix FAB size when label is hidden */
.mdc-fab--extended.label-hidden {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    padding: 16px;
}

.mdc-fab--extended.label-hidden .mdc-fab__icon {
    margin-right: 0;
}

.mdc-fab--extended.label-hidden .mdc-fab__label {
    display: none;
}

/* Small Input Variants */
.mdc-text-field.mdc-small-input {
    height: 40px;
}

.mdc-text-field.mdc-small-input .mdc-text-field__input {
    padding: 8px 12px;
    font-size: 14px;
}

.mdc-text-field.mdc-small-input.mdc-text-field--outlined:not(.mdc-search-input) .mdc-text-field__input {
    padding: 25px 12px;
    height: 40px;
}

/* .mdc-text-field.mdc-small-input.mdc-text-field--with-leading-icon .mdc-text-field__input {
    padding-left: 36px;
} */

.mdc-text-field.mdc-small-input .mdc-text-field__icon {
    font-size: 18px;
}

.mdc-text-field.mdc-small-input .mdc-floating-label {
    font-size: 14px;
}

.mdc-text-field.mdc-small-input.mdc-text-field--outlined .mdc-floating-label {
    top: 50%;
    transform: translateY(-50%);
}

.mdc-text-field.mdc-small-input.mdc-text-field--outlined.mdc-text-field--label-floating .mdc-floating-label {
    top: 0;
    transform: translateY(-50%) scale(0.75);
}



.mdc-select.mdc-small-input {
    height: 40px;
}

.mdc-select.mdc-small-input .mdc-select__anchor {
    height: 40px;
    min-height: 40px;
}

.mdc-select.mdc-small-input .mdc-select__selected-text {
    font-size: 14px;
    line-height: 24px;
    padding-top: 8px;
    padding-bottom: 8px;
}

.mdc-select.mdc-small-input .mdc-select__dropdown-icon {
    width: 20px;
    height: 20px;
}

/* Header Filter Select */
.mdc-data-table__header-cell .mdc-select {
    min-width: 100px;
    position: relative;
    z-index: 100;
}

.mdc-data-table__header-cell .mdc-select .mdc-select__menu {
    z-index: 1500;
}

.mdc-data-table__header-cell .mdc-menu-surface {
    z-index: 1500;
}

.mdc-data-table__header-cell .mdc-select__menu .mdc-menu-surface {
    z-index: 1500;
}

/* Header cell styling with controlled overflow */
.mdc-data-table__header-cell {
    position: relative;
    overflow: visible; /* Keep for dropdowns */
}

/* Sortable header cells need contained ripples */
.mdc-data-table__header-cell--sortable {
    overflow: hidden; /* Contain ripples for sortable headers */
}

/* Exception for header cells with dropdowns - use specific class */
.mdc-data-table__header-cell.has-dropdown {
    overflow: visible; /* Allow dropdown overflow only for filter headers */
}

.mdc-data-table__header-cell .mdc-select {
    position: relative;
    z-index: 1000;
}

/* Table container overflow handled above */

.mdc-data-table {
    overflow: visible; /* Keep for dropdowns */
}

/* Table row ripple containment */
.mdc-data-table__row {
    position: relative;
    overflow: hidden; /* Contain ripples in rows */
}

/* Specific containment for clickable table rows */
.mdc-data-table__row.table-row {
    position: relative;
    overflow: hidden; /* Contain ripples in clickable rows */
    cursor: pointer;
}

/* Table cell ripple containment */
.mdc-data-table__cell {
    position: relative;
    overflow: hidden; /* Contain ripples in cells */
}

/* Allow overflow for actions column specifically */
.mdc-data-table__cell:nth-child(12) {
    overflow: visible !important; /* Override for dropdown visibility */
}

/* Checked row styling */
.mdc-data-table__row.row-selected {
    background-color: var(--primary-alpha-8) !important;
    transition: background-color 0.2s ease;
}

.mdc-data-table__row.row-selected:hover {
    background-color: var(--primary-alpha-12) !important;
}

/* Action buttons in table cells */
.table-actions {
    position: relative;
    overflow: hidden; /* Contain ripples in action area */
    display: flex;
    gap: 4px;
}

.table-actions .mdc-icon-button {
    position: relative;
    overflow: hidden; /* Contain ripples in individual buttons */
    border-radius: 50%;
}

/* Responsive Design */
@media (min-width: 1024px) {
    .students-list {
        display: none;
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .sidebar.collapsed {
        transform: translateX(-100%);
    }

    /* Mobile submenu adjustments */
    .submenu-item {
        padding: 12px 24px 12px 48px;
        font-size: 15px;
    }

    .submenu-item-level-2 {
        padding: 10px 24px 10px 40px;
        font-size: 14px;
    }

    .submenu-level-2 {
        margin-left: 16px;
    }

    /* Adjust hierarchy lines for mobile */
    .submenu-item::before {
        left: 24px;
    }

    /* Mobile sidebar section spacing adjustments */
    .sidebar-section {
        padding: 16px 0;
    }

    .sidebar-section:not(:first-child) {
        padding: 8px 0 8px 0;
        /* margin-top: 12px; */
    }

    /* .sidebar-section:not(:last-child) {
        padding-bottom: 16px;
        margin-bottom: 6px;
    } */

    .sidebar-section-title {
        top: -8px;
        left: 16px;
        padding: 0 10px;
        font-size: 9px;
    }

    .submenu-item::after {
        left: 24px;
        width: 12px;
    }

    .submenu-item-level-2::before {
        left: 16px;
    }

    .submenu-item-level-2::after {
        left: 16px;
        width: 10px;
    }

    .main-content {
        margin-left: 0;
        padding-top: 80px; /* Account for fixed header on mobile */
    }

    .main-content.expanded {
        margin-left: 0;
    }

    /* Fixed header adjustments for mobile */
    .content-header {
        left: 0;
        padding: 8px 16px;
    }

    .content-header.expanded {
        left: 0;
    }

    .bottom-sheet {
        max-height: 70vh;
    }

    /* Table Controls Mobile */
    /* .table-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    */

    .table-controls .mdc-text-field {
        min-width: auto;
        max-width: none !important;
    }

    .per-page-container {
        justify-content: space-between;
    }

    /* Table Mobile Responsiveness */
    .mdc-data-table__table-container {
        overflow-x: auto;
        overflow-y: visible;
    }

    .mdc-data-table__header-cell,
    .mdc-data-table__cell {
        padding: 12px 8px;
        font-size: 13px;
    }

    .student-photo {
        width: 50px;
        height: 50px;
    }

    /* Pagination Mobile */
    .table-pagination {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }

    .pagination-controls {
        justify-content: center;
    }
}

/* Table Row Transitions */
.mdc-data-table__row.table-row {
    opacity: 1;
    transform: translateY(0);
    transition: background-color 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease;
}

.mdc-data-table__row.table-row:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Additional containment for table row ripples */
.mdc-data-table__row .ripple {
    /* Inherit from general ripple styles but ensure containment */
    clip-path: inset(0);
}

/* Checkbox column styling */
.mdc-data-table__header-cell--checkbox,
.mdc-data-table__cell--checkbox {
    width: 48px;
    padding-left: 16px;
    padding-right: 8px;
}

.mdc-data-table__header-row-checkbox,
.mdc-data-table__row-checkbox {
    margin: 0;
}

/* Ensure checkbox ripples are contained */
.mdc-checkbox {
    position: relative;
    overflow: hidden;
}

.mdc-checkbox__ripple {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

/* Loading State for Table - Removed as we now use targeted overlays */

/* Smooth transitions for controls */
.table-controls .mdc-text-field,
.table-controls .mdc-select {
    transition: all 0.3s ease;
}

.table-controls .mdc-text-field:focus-within,
.table-controls .mdc-select:focus-within {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--primary-alpha-24);
}

/* Modal Component Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--shadow-medium);
    z-index: 2500;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: var(--background-surface);
    border-radius: 12px;
    box-shadow: 0 24px 38px 3px var(--shadow-light),
                0 9px 46px 8px var(--shadow-light),
                0 11px 15px -7px var(--shadow-medium);
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    transform: scale(0.8) translateY(-20px);
    transition: transform 0.3s ease;
}

.modal-overlay.active .modal {
    transform: scale(1) translateY(0);
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 24px 0 8px;
    border-bottom: 1px solid var(--border-light);
    padding-bottom: 16px;
    margin-bottom: 0;
}

.modal-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-primary);
    margin: 0;
}

.modal-close {
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    background-color: var(--surface-hover);
}

.modal-close .material-icons {
    font-size: 24px;
    color: var(--text-secondary);
}

.modal-content {
    padding: 24px;
    flex: 1;
    overflow-y: auto;
}

.modal-form {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 24px 24px 16px;
    border-top: 1px solid var(--border-light);
}

.modal-actions .mdc-button--raised {
    background-color: var(--primary-color) !important;
    color: var(--text-on-primary) !important;
}

.modal-actions .mdc-button--raised:hover {
    background-color: var(--primary-light) !important;
}

.modal-actions .mdc-button--outlined {
    border-color: var(--border-medium) !important;
    color: var(--text-secondary) !important;
}

.modal-actions .mdc-button--outlined:hover {
    background-color: var(--surface-hover) !important;
}

/* Form Field Styles */
.form-field {
    display: flex;
    flex-direction: column;
}

.form-field-row {
    display: flex;
    gap: 16px;
}

.form-field-row .form-field {
    flex: 1;
}

/* Modal Form Specific Styles */
.modal-form .mdc-text-field,
.modal-form .mdc-select {
    margin-bottom: 12px;
}

.modal-form .mdc-text-field--outlined,
.modal-form .mdc-select--outlined {
    height: 48px;
}

.modal-form .mdc-text-field--outlined .mdc-text-field__input {
    padding: 12px 16px;
    font-size: 14px;
}

.modal-form .mdc-select--outlined .mdc-select__anchor {
    height: 48px;
    padding: 12px 16px;
}

.modal-form .mdc-floating-label {
    font-size: 14px;
    color: var(--text-secondary);
}

.modal-form .mdc-floating-label--float-above {
    font-size: 12px;
    color: var(--primary-color);
}

.mdc-floating-label {
    border-color: var(--primary-color) !important;
}

.modal-form .mdc-notched-outline__leading,
.modal-form .mdc-notched-outline__trailing {
    border-color: var(--border-medium);
}

.modal-form .mdc-notched-outline--notched .mdc-notched-outline__leading,
.modal-form .mdc-notched-outline--notched .mdc-notched-outline__trailing {
    border-color: var(--primary-color);
}

/* Large Modal Component (3 Columns) */
.modal--large {
    max-width: 1200px;
    width: 95%;
}

.modal--large .modal-content {
    padding: 32px;
}

.modal--large .modal-form {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 24px;
    align-items: start;
}

.modal--large .form-field {
    margin-bottom: 16px;
}

.modal--large .form-field--full-width {
    grid-column: 1 / -1;
}

.modal--large .form-field--two-columns {
    grid-column: span 2;
}

.modal--large .form-section {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.modal--large .form-section-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--primary-color);
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 2px solid var(--primary-alpha-12);
}

.modal--large .mdc-text-field,
.modal--large .mdc-select {
    margin-bottom: 0;
}


/* Responsive Large Modal */
@media (max-width: 1024px) {
    .modal--large {
        max-width: 900px;
    }

    .modal--large .modal-form {
        grid-template-columns: 1fr 1fr;
        gap: 20px;
    }

    .modal--large .form-field--full-width {
        grid-column: 1 / -1;
    }

    .modal--large .form-field--two-columns {
        grid-column: span 2;
    }
}

@media (max-width: 768px) {
    .modal--large {
        width: 100%;
        height: 100%;
        max-height: 100vh;
        border-radius: 0;
        margin: 0;
    }

    .modal--large .modal-form {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .modal--large .form-field--full-width,
    .modal--large .form-field--two-columns {
        grid-column: 1;
    }

    .modal--large .form-field {
        margin-bottom: 8px;
    }

    .modal--large .modal-content {
        padding: 16px;
    }
}

/* Regular Modal Responsive */
@media (max-width: 768px) {
    .modal:not(.modal--large) {
        width: 100%;
        height: 100%;
        max-height: 100vh;
        border-radius: 0;
        margin: 0;
    }

    .modal-overlay {
        padding: 0;
        align-items: stretch;
    }

    .modal:not(.modal--large) .modal-header,
    .modal:not(.modal--large) .modal-content,
    .modal:not(.modal--large) .modal-actions {
        padding-left: 16px;
        padding-right: 16px;
    }

    .form-field-row {
        flex-direction: column;
        gap: 0;
    }

    .modal:not(.modal--large) .modal-form .mdc-text-field,
    .modal:not(.modal--large) .modal-form .mdc-select {
        margin-bottom: 8px;
    }
}

/* Confirmation Dialog Component Styles */
.confirm-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1100;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    padding: 16px;
}

.confirm-dialog-overlay.active {
    opacity: 1;
    visibility: visible;
}

.confirm-dialog {
    background: var(--background-surface);
    border-radius: 12px;
    box-shadow: 0 24px 38px 3px var(--shadow-light),
                0 9px 46px 8px var(--shadow-medium),
                0 11px 15px -7px var(--shadow-dark);
    max-width: 400px;
    width: 100%;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    transform: scale(0.8) translateY(20px);
    transition: transform 0.3s ease;
    overflow: hidden;
}

.confirm-dialog-overlay.active .confirm-dialog {
    transform: scale(1) translateY(0);
}

.confirm-dialog-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 24px 24px 16px 24px;
}

.confirm-dialog-icon .material-icons {
    font-size: 48px;
    color: var(--warning-color, #ff9800);
}

.confirm-dialog-icon.error .material-icons {
    color: var(--error-color, #f44336);
}

.confirm-dialog-icon.info .material-icons {
    color: var(--info-color, #2196f3);
}

.confirm-dialog-icon.success .material-icons {
    color: var(--success-color, #4caf50);
}

.confirm-dialog-content {
    padding: 0 24px 24px 24px;
    text-align: center;
}

.confirm-dialog-title {
    font-size: 20px;
    font-weight: 500;
    color: var(--text-primary);
    margin: 0 0 12px 0;
}

.confirm-dialog-message {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.5;
    margin: 0;
}

.confirm-dialog-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 0 24px 24px 24px;
}

.confirm-dialog-actions .mdc-button {
    min-width: 80px;
}

.confirm-dialog-actions .mdc-button--raised {
    background-color: var(--primary-color) !important;
    color: var(--text-on-primary) !important;
}

.confirm-dialog-actions .mdc-button--raised:hover {
    background-color: var(--primary-light) !important;
}

.confirm-dialog-actions .mdc-button--raised.confirm-dialog-confirm--danger {
    background-color: var(--error-color, #f44336) !important;
    color: white !important;
}

.confirm-dialog-actions .mdc-button--raised.confirm-dialog-confirm--danger:hover {
    background-color: var(--error-dark, #d32f2f) !important;
}

.confirm-dialog-actions .mdc-button--outlined {
    border-color: var(--border-medium) !important;
    color: var(--text-secondary) !important;
}

.confirm-dialog-actions .mdc-button--outlined:hover {
    background-color: var(--surface-hover) !important;
}

/* Responsive Confirmation Dialog */
@media (max-width: 480px) {
    .confirm-dialog {
        max-width: 100%;
        margin: 0 8px;
        border-radius: 8px;
    }

    .confirm-dialog-icon {
        padding: 20px 20px 12px 20px;
    }

    .confirm-dialog-icon .material-icons {
        font-size: 40px;
    }

    .confirm-dialog-content {
        padding: 0 20px 20px 20px;
    }

    .confirm-dialog-title {
        font-size: 18px;
    }

    .confirm-dialog-message {
        font-size: 13px;
    }

    .confirm-dialog-actions {
        padding: 0 20px 20px 20px;
        flex-direction: column-reverse;
        gap: 8px;
    }

    .confirm-dialog-actions .mdc-button {
        width: 100%;
        min-width: auto;
    }
}

/* Mobile Lazy Loading Styles */
.loading-more-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 24px 16px;
    margin: 16px 0;
    gap: 12px;
}

.loading-more-spinner .spinner-container {
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-more-spinner .mdc-circular-progress {
    width: 32px;
    height: 32px;
}

.loading-more-spinner .mdc-circular-progress__determinate-track,
.loading-more-spinner .mdc-circular-progress__determinate-circle {
    stroke: var(--primary-color);
}

.loading-more-spinner .mdc-circular-progress__indeterminate-circle-graphic circle {
    stroke: var(--primary-color);
}

.loading-more-text {
    font-size: 14px;
    color: var(--text-secondary);
    text-align: center;
}

.end-of-results {
    display: flex;
    justify-content: center;
    padding: 32px 16px;
    margin-top: 16px;
}

.end-of-results-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    text-align: center;
}

.end-of-results-content .material-icons {
    font-size: 48px;
    color: var(--success-color, #4caf50);
    margin-bottom: 8px;
}

.end-of-results-text {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-primary);
}

.end-of-results-count {
    font-size: 14px;
    color: var(--text-secondary);
}

/* Hide lazy loading components on desktop */
@media (min-width: 769px) {
    .loading-more-spinner,
    .end-of-results {
        display: none;
    }
}

/* Scroll Behavior Styles */
.mdc-fab {
    transition: transform 0.3s ease;
}

.mdc-fab__label {
    transition: opacity 0.2s ease, transform 0.2s ease;
}

.bottom-app-bar {
    transition: transform 0.3s ease;
    will-change: transform;
}

/* Ensure FAB label doesn't cause layout shifts when hidden */
.mdc-fab__label {
    white-space: nowrap;
    overflow: hidden;
}

/* Smooth transitions for scroll-based animations */
@media (prefers-reduced-motion: no-preference) {
    .mdc-fab,
    .mdc-fab__label,
    .bottom-app-bar {
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    }
}

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
    .mdc-fab,
    .mdc-fab__label,
    .bottom-app-bar {
        transition: none;
    }
}

/* Offcanvas Styles */
.offcanvas-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--shadow-medium);
    z-index: 3000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.offcanvas-overlay.active {
    opacity: 1;
    visibility: visible;
}

.offcanvas {
    position: fixed;
    top: 0;
    right: 0;
    width: 360px;
    height: 100vh;
    background: var(--background-surface);
    box-shadow: -4px 0 16px var(--shadow-medium);
    transform: translateX(100%);
    transition: transform 0.3s ease;
    z-index: 3001;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.offcanvas.active {
    transform: translateX(0);
}

.offcanvas-header {
    padding: 6px 24px;
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--primary-gradient);
    color: var(--text-on-primary);
    min-height: 56px;
}

.offcanvas-title {
    font-size: 18px;
    font-weight: 500;
}

.offcanvas-close {
    background: transparent;
    border: none;
    color: var(--text-on-primary);
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.2s;
    position: relative;
    overflow: hidden;
}

.offcanvas-close:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.offcanvas-content {
    flex: 1;
    padding: 0;
}

.offcanvas-profile {
    padding: 24px;
    display: flex;
    align-items: center;
    background: var(--background-secondary);
    border-bottom: 1px solid var(--border-light);
}

.profile-avatar {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: var(--surface-selected);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
}

.profile-avatar .material-icons {
    font-size: 32px;
    color: var(--primary-color);
}

.profile-info {
    flex: 1;
}

.profile-name {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.profile-email {
    font-size: 14px;
    color: var(--text-secondary);
}

.offcanvas-menu {
    padding: 8px 0;
}

.offcanvas-menu-item {
    display: flex;
    align-items: center;
    padding: 16px 24px;
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
    overflow: hidden;
}

.offcanvas-menu-item:hover {
    background-color: var(--surface-hover);
}

.offcanvas-menu-item.logout {
    color: var(--error-color);
}

.offcanvas-menu-item.logout:hover {
    background-color: #ffebee;
}

.offcanvas-menu-item .material-icons:first-child {
    margin-right: 16px;
    font-size: 24px;
    color: var(--text-secondary);
}

.offcanvas-menu-item.logout .material-icons:first-child {
    color: var(--error-color);
}

.menu-item-content {
    flex: 1;
}

.menu-item-title {
    font-size: 16px;
    font-weight: 400;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.menu-item-subtitle {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.3;
}

.offcanvas-menu-item.logout .menu-item-title {
    color: var(--error-color);
}

.menu-item-arrow {
    color: var(--text-disabled);
    font-size: 20px;
}

.offcanvas-menu-item.logout .menu-item-arrow {
    display: none;
}

.offcanvas-divider {
    height: 1px;
    background: var(--border-light);
    margin: 8px 0;
}

/* Filter Form Styles */
.filter-form {
    padding: 24px;
}

.filter-group {
    margin-bottom: 24px;
}

.filter-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #424242;
    margin-bottom: 8px;
}

.filter-select {
    width: 100%;
}

.filter-select .mdc-select__anchor {
    height: 48px;
}

.date-filter-container {
    display: flex;
    align-items: center;
    gap: 12px;
}

.filter-date-input {
    flex: 1;
}

.filter-date-input .mdc-text-field__input {
    cursor: pointer;
}

.date-separator {
    font-size: 14px;
    color: #757575;
    white-space: nowrap;
}

.filter-actions {
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #e0e0e0;
}   

.mdc-button--outlined {
    color: var(--primary-color) !important;
}

.filter-actions .mdc-button {
    width: 100%;
}

/* Flatpickr customization to match Material Design */
.flatpickr-calendar {
    font-family: 'Roboto', sans-serif;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    border: none;
}

.flatpickr-day.selected {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.flatpickr-day.selected:hover {
    background: var(--primary-light);
    border-color: var(--primary-light);
}

.flatpickr-day:hover {
    background: var(--surface-selected);
}

.flatpickr-months .flatpickr-month {
    background: var(--primary-color);
    color: var(--text-on-primary);
}

.flatpickr-current-month .flatpickr-monthDropdown-months {
    background: var(--primary-color);
    color: var(--text-on-primary);
}

.flatpickr-current-month input.cur-year {
    background: var(--primary-color);
    color: var(--text-on-primary);
}

.flatpickr-weekdays {
    background: var(--background-primary);
}

.flatpickr-weekday {
    color: var(--text-secondary);
    font-weight: 500;
}

/* Responsive Design */
@media (min-width: 769px) {
    .mdc-fab {
        display: none;
    }

    /* Hide QR FAB on desktop */
    .qr-fab {
        display: none !important;
    }

    .add-btn {
        display: inline-flex;
    }

    /* Hide bottom app bar on desktop */
    .bottom-app-bar {
        display: none !important;
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        z-index: 5000;
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
        padding-left: 10px;
        padding-right: 10px;
    }

    .main-content.expanded {
        margin-left: 0;
    }

    .students-list {
        display: block;
    }

    .offcanvas {
        width: 100%;
        max-width: 360px;
    }

    .add-btn {
        display: none;
    }

    /* Show QR FAB on mobile */
    .qr-fab {
        display: flex !important;
    }

    .page-title {
        font-size: 18px; /* Reduced from 20px to accommodate item count */
    }

    .content-header .actions {
        gap: 4px;
    }

    .filter-chips-container {
        margin-bottom: 12px;
    }

    /* Quick filter chips mobile adjustments - Material Tab Style */
    .quick-filter-chips {
        display: flex;
        flex-wrap: nowrap; /* Prevent wrapping - force single line */
        gap: 0;
        margin-bottom: 8px;
        justify-content: flex-start;
        overflow-x: auto;
        overflow-y: hidden;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE/Edge */
        padding: 0 16px;
        margin-left: -16px;
        margin-right: -16px;
        width: 100vw; /* Ensure full width */
    }

    /* Hide scrollbar for webkit browsers */
    .quick-filter-chips::-webkit-scrollbar {
        display: none;
    }

    .quick-filter-chip {
        flex: none;
        flex-shrink: 0; /* Prevent shrinking */
        flex-direction: row;
        padding: 8px 16px;
        font-size: 12px;
        border-radius: 20px;
        gap: 6px;
        min-height: 32px;
        justify-content: center;
        align-items: center;
        text-align: center;
        white-space: nowrap;
        margin-right: 8px;
        min-width: fit-content; /* Ensure proper width */
    }

    .quick-filter-chip:last-child {
        margin-right: 16px; /* Extra margin for last chip */
    }

    .quick-filter-chip .material-icons {
        font-size: 16px;
        margin-bottom: 0;
    }

    .quick-filter-chip span:last-child {
        display: inline; /* Show text labels inline with icons */
        line-height: 1;
        font-weight: 500;
        white-space: nowrap;
    }

    /* Ensure chips don't get too small on very small screens */
    @media (max-width: 360px) {
        .quick-filter-chip {
            font-size: 11px;
            padding: 6px 12px;
            min-height: 30px;
            gap: 4px;
        }

        .quick-filter-chip .material-icons {
            font-size: 14px;
        }
    }

    /* Show search icon on mobile */
    .search-icon-mobile {
        display: inline-flex !important;
    }

    /* Show bottom app bar on mobile */
    .bottom-app-bar {
        display: flex !important;
    }

    /* Add bottom padding to main content for bottom app bar */
    .main-content {
        padding-bottom: 50px;
    }

    /* Adjust FAB position to avoid bottom app bar */
    .mdc-fab {
        bottom: 80px;
    }

    .qr-fab {
        bottom: 160px !important; /* Position above the add student FAB on mobile */
    }

    /* Mobile responsive adjustments for QR scanner */
    .qr-scanner-modal {
        width: 95vw;
        max-width: none;
        margin: 16px;
        max-height: calc(100vh - 32px);
    }

    .qr-scanner-content {
        padding: 16px;
    }

    #qr-reader {
        max-width: 100%;
    }

    /* Reduce spacing between back button and title on mobile */
    .content-header {
        margin-bottom: 8px;
    }

    .content-header .material-icons:first-child {
        margin-right: 4px; /* Reduced from 8px */
    }

    /* Make student list items more compact on mobile */
    .student-item {
        padding: 12px 0;
    }

    /* Adjust students list height to prevent content hiding */
    .students-list {
        max-height: calc(100vh - 56px - 8px - 60px - 50px);
        padding-bottom: 20px;
    }

    /* Mobile-specific student list styling */
    .student-name-ar {
        color: var(--text-secondary) !important; /* Gray color like birth date */
    }

    .student-grades .grade-fr,
    .student-grades .grade-ar {
        background: var(--surface-hover) !important;
        color: var(--text-secondary) !important; /* Gray color */
        font-weight: bold !important; /* Bold for levels */
        padding: 2px 6px !important;
        border-radius: 4px !important;
        font-size: 11px !important;
    }

    .student-grades .grade-separator {
        color: var(--text-secondary) !important;
    }
}

/* Hide mobile search icon on desktop */
.search-icon-mobile {
    display: none;
}

/* Dark Mode Specific Styles */
[data-theme="dark"] .mdc-data-table__table {
    background-color: var(--background-surface);
    color: var(--text-primary);
}

[data-theme="dark"] .mdc-data-table__header-cell {
    background-color: var(--background-secondary);
    color: var(--text-primary);
    border-bottom-color: var(--border-light);
}

[data-theme="dark"] .mdc-data-table__cell {
    border-bottom-color: var(--border-light);
}

/* .mdc-text-field--outlined:not(.mdc-text-field--focused) .mdc-notched-outline__leading,
.mdc-text-field--outlined:not(.mdc-text-field--focused) .mdc-notched-outline__notch,
.mdc-text-field--outlined:not(.mdc-text-field--focused) .mdc-notched-outline__trailing {
    border-color: var(--border-medium) !important;
} */

.mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,
.mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,
.mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing, 
.mdc-select--focused:not(.mdc-select--disabled) .mdc-notched-outline__leading,
.mdc-select--focused:not(.mdc-select--disabled) .mdc-notched-outline__notch,
.mdc-select--focused:not(.mdc-select--disabled) .mdc-notched-outline__trailing 
{
    border-color: var(--primary-color) !important;
}

[data-theme="dark"] .mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,
[data-theme="dark"] .mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,
[data-theme="dark"] .mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing, 
[data-theme="dark"] .mdc-select--outlined:not(.mdc-select--disabled) .mdc-notched-outline__leading,
[data-theme="dark"] .mdc-select--outlined:not(.mdc-select--disabled) .mdc-notched-outline__notch,
[data-theme="dark"] .mdc-select--outlined:not(.mdc-select--disabled) .mdc-notched-outline__trailing  
{
    border-color: var(--border-medium) !important;
}


[data-theme="dark"] .mdc-text-field--outlined .mdc-text-field__input {
    color: var(--text-primary);
}

/* Dark mode support for quick filter chips */
[data-theme="dark"] .quick-filter-chip {
    background: var(--background-surface);
    color: var(--text-secondary);
    border-color: var(--border-medium);
}

[data-theme="dark"] .quick-filter-chip:hover {
    background: var(--surface-hover);
    border-color: var(--border-light);
}

[data-theme="dark"] .quick-filter-chip.active {
    background: var(--primary-color);
    color: var(--text-on-primary);
    border-color: var(--primary-color);
}

[data-theme="dark"] .quick-filter-chip.active:hover {
    background: var(--primary-light);
    border-color: var(--primary-light);
}

/* Dark mode support for no data component */
[data-theme="dark"] .no-data-icon {
    color: var(--text-disabled);
}

[data-theme="dark"] .no-data-title {
    color: var(--text-primary);
}

[data-theme="dark"] .no-data-message {
    color: var(--text-secondary);
}

[data-theme="dark"] .mdc-floating-label {
    color: var(--text-secondary);
}

[data-theme="dark"] .table-action-menu {
    background-color: var(--background-surface);
    border-color: var(--border-light);
}

[data-theme="dark"] .table-action-menu-item {
    color: var(--text-primary);
}

[data-theme="dark"] .table-action-menu-item:hover {
    background-color: var(--surface-hover);
}

[data-theme="dark"] .table-action-menu-subtitle {
    color: var(--text-secondary);
}

/* Dark mode transition */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Mobile Search Overlay */
.mobile-search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--background-surface);
    z-index: 4000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.mobile-search-overlay.active {
    opacity: 1;
    visibility: visible;
}

.mobile-search-container {
    padding: 16px;
    background: var(--background-surface);
    box-shadow: 0 2px 8px var(--shadow-light);
}

.mobile-search-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.mobile-search-back {
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mobile-search-back:hover {
    background-color: var(--surface-hover);
}

.mobile-search-back .material-icons {
    font-size: 24px;
    color: var(--text-secondary);
}

.mobile-search-input-container {
    flex: 1;
}

.mobile-search-input {
    width: 100%;
    padding: 12px 16px;
    border: none;
    background: var(--background-secondary);
    border-radius: 24px;
    font-size: 16px;
    color: var(--text-primary);
    outline: none;
    transition: background-color 0.2s;
}

.mobile-search-input:focus {
    background: var(--surface-selected);
}

.mobile-search-input::placeholder {
    color: var(--text-secondary);
}

.mobile-search-results {
    max-height: calc(100vh - 120px);
    overflow-y: auto;
    padding: 0 16px;
}

.mobile-search-result-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid var(--border-light);
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
    overflow: hidden;
}

.mobile-search-result-item:hover {
    background-color: var(--surface-hover);
}

.mobile-search-result-item:last-child {
    border-bottom: none;
}

.mobile-search-result-photo {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-size: cover;
    background-position: center;
    margin-right: 12px;
    flex-shrink: 0;
}

.mobile-search-result-info {
    flex: 1;
}

.mobile-search-result-name {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.mobile-search-result-details {
    font-size: 14px;
    color: var(--text-secondary);
}

.mobile-search-no-results {
    text-align: center;
    padding: 48px 16px;
    color: var(--text-secondary);
}

.mobile-search-no-results .material-icons {
    font-size: 48px;
    margin-bottom: 16px;
    display: block;
    color: var(--text-disabled);
}

/* ========================================
   BULK ACTIONS COMPONENT
   ======================================== */

/* Bulk Action Bar */
.bulk-action-bar {
    position: fixed;
    bottom: 24px;
    left: 50%;
    transform: translateX(-50%) translateY(20px);
    background: var(--background-surface);
    border-radius: 28px;
    box-shadow: 0 8px 32px var(--shadow-medium);
    padding: 12px 24px;
    display: flex;
    align-items: center;
    gap: 24px;
    z-index: 1200;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 320px;
    border: 1px solid var(--border-light);
}

.bulk-action-bar.visible {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(0);
}

/* Selected Count */
.bulk-selected-count {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    white-space: nowrap;
}

/* Action Buttons Container */
.bulk-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: auto;
}

/* Base Action Button */
.bulk-action-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border: none;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    background: transparent;
    text-decoration: none;
}

.bulk-action-btn:focus {
    outline: none;
}

.bulk-action-btn:disabled {
    opacity: var(--opacity-disabled);
    cursor: not-allowed;
}

/* Archive Button */
.bulk-action-btn.archive {
    background: var(--surface-hover);
    color: var(--text-primary);
    border: 1px solid var(--border-light);
}

.bulk-action-btn.archive:hover:not(:disabled) {
    background: var(--border-light);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px var(--shadow-light);
}

.bulk-action-btn.archive:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 2px 8px var(--shadow-light);
}

/* Delete Button */
.bulk-action-btn.delete {
    background: var(--error-color);
    color: white;
}

.bulk-action-btn.delete:hover:not(:disabled) {
    background: #d32f2f;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
}

.bulk-action-btn.delete:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(244, 67, 54, 0.3);
}



/* Ripple Effect */
.bulk-action-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.bulk-action-btn:active::before {
    width: 100px;
    height: 100px;
}

/* Material Icons Styling */
.bulk-action-btn .material-icons {
    font-size: 16px;
    margin-right: 4px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .bulk-action-bar {
        bottom: 80px; /* Above bottom navigation */
        left: 16px;
        right: 16px;
        transform: none;
        min-width: auto;
        padding: 12px 16px;
        gap: 16px;
    }

    .bulk-action-bar.visible {
        transform: none;
    }

    .bulk-actions {
        gap: 6px;
    }

    .bulk-action-btn {
        padding: 8px 12px;
        font-size: 13px;
        gap: 6px;
    }

    .bulk-action-btn .material-icons {
        font-size: 14px;
        margin-right: 2px;
    }
}

/* Dark Mode Support */
[data-theme="dark"] .bulk-action-bar {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
}

[data-theme="dark"] .bulk-action-btn.archive {
    background: var(--surface-hover);
    border-color: var(--border-medium);
}

[data-theme="dark"] .bulk-action-btn.archive:hover:not(:disabled) {
    background: var(--border-medium);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .bulk-action-bar {
        bottom: 80px; /* Above bottom navigation */
        left: 16px;
        right: 16px;
        transform: none;
        min-width: auto;
        padding: 12px 16px;
        gap: 16px;
    }

    .bulk-action-bar.visible {
        transform: none;
    }

    .bulk-actions {
        gap: 6px;
    }

    .bulk-action-btn {
        padding: 8px 12px;
        font-size: 13px;
        gap: 6px;
    }

    .bulk-action-btn svg {
        width: 14px;
        height: 14px;
    }
}

/* Dark Mode Support */
[data-theme="dark"] .bulk-action-bar {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
}

[data-theme="dark"] .bulk-action-btn.archive {
    background: var(--surface-hover);
    border-color: var(--border-medium);
}

[data-theme="dark"] .bulk-action-btn.archive:hover:not(:disabled) {
    background: var(--border-medium);
}

/* ========================================
   DASHBOARD COMPONENTS
   ======================================== */

/* Dashboard Stats Cards */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
}

.stat-card {
    background: var(--background-surface);
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px var(--shadow-light);
    border: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    gap: 16px;
    transition: all 0.2s ease;
}

.stat-card:hover {
    box-shadow: 0 4px 16px var(--shadow-medium);
    transform: translateY(-2px);
}

.stat-icon {
    width: 56px;
    height: 56px;
    border-radius: 12px;
    background: var(--primary-alpha-12);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.stat-icon .material-icons {
    font-size: 28px;
    color: var(--primary-color);
}

.stat-icon.warning {
    background: rgba(255, 152, 0, 0.12);
}

.stat-icon.warning .material-icons {
    color: var(--warning-color);
}

.stat-icon.success {
    background: rgba(76, 175, 80, 0.12);
}

.stat-icon.success .material-icons {
    color: var(--success-color);
}

.stat-icon.info {
    background: rgba(33, 150, 243, 0.12);
}

.stat-icon.info .material-icons {
    color: var(--info-color);
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 28px;
    font-weight: 600;
    color: var(--text-primary);
    line-height: 1.2;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 8px;
}

.stat-change {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 500;
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-change.negative {
    color: var(--error-color);
}

.stat-change .material-icons {
    font-size: 16px;
}

/* Dashboard Sections */
.dashboard-section {
    margin-bottom: 32px;
}

.section-header {
    margin-bottom: 20px;
}

.section-title {
    font-size: 20px;
    font-weight: 500;
    color: var(--text-primary);
    margin: 0;
}

/* Quick Actions Grid */
.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 16px;
}

.quick-action-card {
    background: var(--background-surface);
    border: 1px solid var(--border-light);
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
}

.quick-action-card:hover {
    box-shadow: 0 4px 12px var(--shadow-light);
    transform: translateY(-1px);
    border-color: var(--primary-color);
}

.quick-action-icon {
    width: 48px;
    height: 48px;
    border-radius: 10px;
    background: var(--primary-alpha-12);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.quick-action-icon .material-icons {
    font-size: 24px;
    color: var(--primary-color);
}

.quick-action-content {
    flex: 1;
}

.quick-action-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.quick-action-subtitle {
    font-size: 14px;
    color: var(--text-secondary);
}

/* Dashboard Row Layout */
.dashboard-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    margin-bottom: 32px;
}

.dashboard-column {
    display: flex;
    flex-direction: column;
}

/* Dashboard Cards */
.dashboard-card {
    background: var(--background-surface);
    border-radius: 12px;
    box-shadow: 0 2px 8px var(--shadow-light);
    border: 1px solid var(--border-light);
    overflow: hidden;
    height: 100%;
}

.card-header {
    padding: 20px 24px 16px 24px;
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-title {
    font-size: 18px;
    font-weight: 500;
    color: var(--text-primary);
    margin: 0;
}

.card-action {
    background: none;
    border: none;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color 0.2s ease;
    color: var(--text-secondary);
}

.card-action:hover {
    background: var(--surface-hover);
}

.card-action .material-icons {
    font-size: 20px;
}

.card-content {
    padding: 0;
}

/* Activity List */
.activity-list {
    padding: 8px 0;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 16px 24px;
    transition: background-color 0.2s ease;
}

.activity-item:hover {
    background: var(--surface-hover);
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: var(--primary-alpha-12);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.activity-icon .material-icons {
    font-size: 20px;
    color: var(--primary-color);
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.activity-subtitle {
    font-size: 13px;
    color: var(--text-secondary);
    margin-bottom: 4px;
}

.activity-time {
    font-size: 12px;
    color: var(--text-disabled);
}

/* Event List */
.event-list {
    padding: 8px 0;
}

.event-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px 24px;
    transition: background-color 0.2s ease;
}

.event-item:hover {
    background: var(--surface-hover);
}

.event-date {
    width: 48px;
    height: 48px;
    border-radius: 10px;
    background: var(--primary-alpha-12);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.event-day {
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-color);
    line-height: 1;
}

.event-month {
    font-size: 10px;
    font-weight: 500;
    color: var(--primary-color);
    text-transform: uppercase;
    line-height: 1;
}

.event-content {
    flex: 1;
}

.event-title {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 6px;
}

.event-time {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: var(--text-secondary);
}

.event-time .material-icons {
    font-size: 14px;
}

/* Dashboard Mobile Responsive */
@media (max-width: 768px) {
    .dashboard-stats {
        grid-template-columns: 1fr;
        gap: 16px;
        margin-bottom: 24px;
    }

    .stat-card {
        padding: 20px;
    }

    .stat-number {
        font-size: 24px;
    }

    .quick-actions-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .quick-action-card {
        padding: 16px;
    }

    .dashboard-row {
        grid-template-columns: 1fr;
        gap: 20px;
        margin-bottom: 24px;
    }

    .card-header {
        padding: 16px 20px 12px 20px;
    }

    .card-title {
        font-size: 16px;
    }

    .activity-item,
    .event-item {
        padding: 12px 20px;
    }

    .activity-icon,
    .event-date {
        width: 36px;
        height: 36px;
    }

    .activity-icon .material-icons {
        font-size: 18px;
    }

    .event-day {
        font-size: 14px;
    }

    .event-month {
        font-size: 9px;
    }
}

/* Dashboard Dark Mode Support */
[data-theme="dark"] .stat-card,
[data-theme="dark"] .quick-action-card,
[data-theme="dark"] .dashboard-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .stat-card:hover,
[data-theme="dark"] .quick-action-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .quick-action-card:hover {
    border-color: var(--primary-light);
}

[data-theme="dark"] .stat-icon.warning {
    background: rgba(255, 152, 0, 0.2);
}

[data-theme="dark"] .stat-icon.success {
    background: rgba(76, 175, 80, 0.2);
}

[data-theme="dark"] .stat-icon.info {
    background: rgba(33, 150, 243, 0.2);
}

/* ========================================
   LEVELS MANAGEMENT COMPONENTS
   ======================================== */

/* Material Design M2 Tabs Customization for Levels */
.mdc-tab-bar {
    margin-bottom: 24px;
}

.mdc-tab__icon {
    font-size: 20px;
}

/* Ensure proper spacing between icon and text in tabs */
.mdc-tab__content {
    gap: 8px;
}

/* Levels Content */
.levels-content {
    opacity: 1;
    transition: opacity 0.2s ease;
}

/* ========================================
   UNIVERSAL TABLE STYLES (COMPACT DESIGN)
   ======================================== */

/* General Data Table Container */
.mdc-data-table {
    background: var(--background-surface);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px var(--shadow-light);
    border: 1px solid var(--border-light);
}

.mdc-data-table__table {
    width: 100%;
    border-collapse: collapse;
}

.mdc-data-table__table td,
.mdc-data-table__table th {
    border-left: none !important;
    border-right: none !important;
}

/* Table Headers */
.mdc-data-table__header-cell {
    background: var(--background-secondary);
    font-weight: 500;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-light);
    border-right: none !important;
    padding: 8px 12px !important;
    font-size: 13px;
    height: 40px;
}

/* Table Rows */
.mdc-data-table__row {
    transition: background-color 0.2s ease;
}

.mdc-data-table__row:hover {
    background: var(--surface-hover);
}

.mdc-data-table__row--selected {
    background: var(--surface-selected);
}

/* Table Cells */
.mdc-data-table__cell {
    padding: 6px 12px !important;
    border-bottom: 1px solid var(--border-light);
    border-right: none !important;
    font-size: 12px;
    line-height: 1.3;
    height: 40px;
}

/* Numeric Columns */
.numeric-cell {
    /* text-align: right; */
    font-weight: 500;
    color: var(--text-primary);
}

/* Checkbox Cells */
.mdc-data-table__cell--checkbox {
    padding: 6px 8px !important;
}

/* Action Buttons in Tables */
.table-actions {
    display: flex;
    gap: 2px;
    align-items: center;
}

.action-btn {
    background: none;
    border: none;
    padding: 4px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--text-secondary);
}

.action-btn:hover {
    background: var(--surface-hover);
    color: var(--text-primary);
}

.action-btn.edit-btn:hover {
    background: var(--primary-alpha-12);
    color: var(--primary-color);
}

.action-btn.delete-btn:hover {
    background: rgba(244, 67, 54, 0.12);
    color: var(--error-color);
}

.action-btn .material-icons {
    font-size: 16px;
}

/* Universal Table Mobile Responsive */
@media (max-width: 768px) {
    .mdc-data-table__table {
        table-layout: auto !important;
        width: 100% !important;
    }

    .mdc-data-table__header-cell {
        padding: 6px 4px !important;
        font-size: 11px;
        height: 36px;
        white-space: nowrap;
        width: auto !important;
    }

    .mdc-data-table__cell {
        padding: 4px 4px !important;
        font-size: 13px;
        height: 36px;
        white-space: nowrap;
        width: auto !important;
    }

    .mdc-data-table__cell--checkbox {
        padding: 4px 2px !important;
        width: 40px !important;
    }

    /* Specific column widths for mobile */
    .mdc-data-table__header-cell:nth-child(1),
    .mdc-data-table__cell:nth-child(1) {
        width: 40px !important; /* Checkbox column */
    }

    .mdc-data-table__header-cell:last-child,
    .mdc-data-table__cell:last-child {
        width: 60px !important; /* Actions column */
    }

    .numeric-cell {
        text-align: right;
        min-width: 50px;
    }

    .table-actions {
        gap: 1px;
        justify-content: center;
    }

    .action-btn {
        padding: 2px;
        min-width: 24px;
        min-height: 24px;
    }

    .action-btn .material-icons {
        font-size: 14px;
    }
}

/* Levels Mobile Responsive */
@media (max-width: 768px) {
    .mdc-tab-bar {
        margin-bottom: 20px;
    }

    .mdc-tab {
        padding: 0 12px;
        min-width: auto;
    }

    .mdc-tab__text-label {
        font-size: 13px;
    }

    .mdc-tab__icon {
        font-size: 18px;
    }
}

/* Universal Table Dark Mode Support */
[data-theme="dark"] .mdc-data-table {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .mdc-data-table__header-cell {
    background: var(--background-primary);
}

[data-theme="dark"] .mdc-data-table__cell {
    color: var(--text-secondary) !important;
}

[data-theme="dark"] .action-btn.edit-btn:hover {
    background: var(--primary-alpha-16);
}

[data-theme="dark"] .action-btn.delete-btn:hover {
    background: rgba(244, 67, 54, 0.2);
}

/* Levels Dark Mode Support - MDC tabs inherit theme colors automatically */

/* ========================================
   RTL (RIGHT-TO-LEFT) SUPPORT
   ======================================== */

/* RTL Support for Arabic and other RTL languages
   To enable RTL, add dir="rtl" to the <html> tag:
   <html dir="rtl">

   Material Design Components automatically handle most RTL transformations,
   but these styles provide additional customizations for our specific layout.
*/

[dir="rtl"] {
    /* Sidebar positioning for RTL */
    .sidebar {
        left: auto;
        right: 0;
        transform: translateX(100%);
    }

    .sidebar.open {
        transform: translateX(0);
    }

    /* Main content adjustment for RTL */
    .main-content {
        margin-left: 0;
        margin-right: 280px;
    }

    /* Content header adjustments */
    .content-header {
        flex-direction: row-reverse;
    }

    .content-header .material-icons {
        transform: scaleX(-1); /* Flip back arrow for RTL */
    }

    /* Table actions alignment */
    .table-actions {
        flex-direction: row-reverse;
    }

    /* Search and filter controls */
    .table-controls {
        flex-direction: row-reverse;
    }

    /* Pagination controls */
    .table-pagination {
        flex-direction: row-reverse;
    }

    .pagination-controls {
        flex-direction: row-reverse;
    }

    /* Modal positioning */
    .modal {
        text-align: right;
    }

    /* Form field adjustments */
    .form-row {
        flex-direction: row-reverse;
    }

    /* Bottom sheet adjustments */
    .bottom-sheet-actions {
        flex-direction: row-reverse;
    }

    /* Bulk actions positioning */
    .bulk-actions {
        flex-direction: row-reverse;
    }

    /* App bar actions */
    .app-bar .actions {
        flex-direction: row-reverse;
    }

    /* Material icons that should be flipped for RTL */
    .material-icons.rtl-flip {
        transform: scaleX(-1);
    }

    /* Tab content spacing for RTL */
    .mdc-tab__content {
        flex-direction: row-reverse;
    }

    /* Table action menu positioning */
    .table-action-menu {
        left: auto;
        right: 0;
    }

    /* Submenu RTL adjustments */
    .submenu-item {
        padding: 10px 56px 10px 24px;
        text-align: right;
    }

    .submenu-item::before {
        left: auto;
        right: 32px;
    }

    .submenu-item::after {
        left: auto;
        right: 32px;
    }

    .submenu-item-with-submenu .expand-icon {
        margin-left: 0;
        margin-right: auto;
    }

    /* RTL submenu icons */
    .submenu-icon {
        margin-right: 0;
        margin-left: 12px;
    }

    .submenu-level-2 {
        margin-left: 0;
        margin-right: 24px;
        border-left: none;
        border-right: 2px solid var(--primary-alpha-24);
    }

    .submenu-item-level-2 {
        padding: 8px 48px 8px 24px;
        text-align: right;
    }

    .submenu-item-level-2::before {
        left: auto;
        right: 24px;
    }

    .submenu-item-level-2::after {
        left: auto;
        right: 24px;
    }
}

/* Mobile RTL adjustments */
@media (max-width: 768px) {
    [dir="rtl"] {
        .main-content {
            margin-right: 0;
        }

        .bottom-nav {
            flex-direction: row-reverse;
        }

        .mobile-list-item {
            text-align: right;
        }

        .mobile-list-item .student-info {
            text-align: right;
        }

        .mobile-list-item .student-actions {
            flex-direction: row-reverse;
        }

        /* Mobile submenu RTL adjustments */
        .submenu-item {
            padding: 12px 48px 12px 24px;
        }

        .submenu-item::before {
            left: auto;
            right: 24px;
        }

        .submenu-item::after {
            left: auto;
            right: 24px;
        }

        .submenu-item-level-2 {
            padding: 10px 40px 10px 24px;
        }

        .submenu-item-level-2::before {
            left: auto;
            right: 16px;
        }

        .submenu-item-level-2::after {
            left: auto;
            right: 16px;
        }

        .submenu-level-2 {
            margin-left: 0;
            margin-right: 16px;
        }
    }
}

/* ========================================
   DASHBOARD STYLES
   ======================================== */

.dashboard-container {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: var(--background-surface);
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px var(--shadow-light);
    display: flex;
    align-items: center;
    gap: 16px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px var(--shadow-medium);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    background: var(--primary-alpha-12);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.stat-icon .material-icons {
    font-size: 24px;
    color: var(--primary-color);
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 28px;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1.2;
}

.stat-label {
    font-size: 14px;
    color: var(--text-secondary);
    margin: 4px 0;
}

.stat-change {
    font-size: 12px;
    font-weight: 500;
    padding: 2px 8px;
    border-radius: 12px;
    display: inline-block;
}

.stat-change.positive {
    background: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
}

.stat-change.negative {
    background: rgba(244, 67, 54, 0.1);
    color: var(--error-color);
}

.stat-change.neutral {
    background: var(--border-light);
    color: var(--text-secondary);
}

/* Column Stat Card - Compact Design */
.stat-card-column {
    background: var(--background-surface);
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 8px var(--shadow-light);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card-column:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px var(--shadow-medium);
}

.stat-card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.stat-header-content {
    flex: 1;
}

.stat-label-main {
    font-size: 13px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 2px;
    line-height: 1.2;
}

.stat-grid-content {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 6px;
}

.stat-grid-item {
    text-align: center;
    padding: 8px 4px;
    border-radius: 6px;
    background: var(--background-secondary);
    transition: background-color 0.2s ease;
}

.stat-grid-item:hover {
    background: var(--primary-alpha-8);
}

.stat-grid-total {
    background: var(--primary-alpha-8);
    /* border: 1px solid var(--primary-alpha-24); */
}

.stat-grid-total:hover {
    background: var(--primary-alpha-16);
}

.stat-number-small {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    line-height: 1.1;
    margin-bottom: 2px;
}

.stat-label-small {
    font-size: 11px;
    color: var(--text-secondary);
    font-weight: 400;
    line-height: 1.2;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.dashboard-grid-2 {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.dashboard-card {
    background: var(--background-surface);
    border-radius: 12px;
    box-shadow: 0 2px 8px var(--shadow-light);
    overflow: hidden;
}

.dashboard-card.full-width {
    grid-column: 1 / -1;
}

.card-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
    color: var(--text-primary);
}

.card-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.card-actions .material-icons {
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.card-actions .material-icons:hover {
    background: var(--surface-hover);
}

/* Material Design Menu Fixes */
.dashboard-card {
    overflow: visible !important;
}

.card-header {
    overflow: visible !important;
}

.card-actions {
    overflow: visible !important;
}

.mdc-menu-surface--anchor {
    position: relative;
}

.mdc-menu {
    z-index: 1000;
}

.mdc-menu .mdc-list {
    padding: 8px 0;
}

.mdc-menu .mdc-list-item {
    height: 48px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
}

.mdc-menu .mdc-list-item:hover {
    background-color: var(--surface-hover);
}

.mdc-menu .mdc-list-item__graphic {
    margin-right: 0;
    font-size: 18px;
    color: var(--text-secondary);
}

.mdc-menu .mdc-list-item__text {
    font-size: 14px;
    color: var(--text-primary);
}

/* HTMX Loading States */
.htmx-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 999;
    border-radius: inherit;
}

[data-theme="dark"] .htmx-loading-overlay {
    background: rgba(0, 0, 0, 0.8);
}

.htmx-loading-overlay.show {
    display: flex;
}

.htmx-loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-light);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: htmx-spin 1s linear infinite;
}

@keyframes htmx-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* HTMX Target Loading States */
.htmx-request {
    position: relative;
    pointer-events: none;
}

.htmx-request::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(229, 221, 221, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 998;
    border-radius: inherit;
}

[data-theme="dark"] .htmx-request::after {
    background: rgba(0, 0, 0, 0.7);
}

/* Material Design Loading Indicator for HTMX */
.htmx-indicator {
    display: none;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
}

.htmx-request .htmx-indicator {
    display: block;
}

.mdc-circular-progress--htmx {
    width: 32px;
    height: 32px;
}

.mdc-circular-progress--htmx .mdc-circular-progress__determinate-circle,
.mdc-circular-progress--htmx .mdc-circular-progress__indeterminate-circle-graphic circle {
    stroke-width: 3;
    stroke: var(--primary-color);
}

.mdc-circular-progress--htmx .mdc-circular-progress__determinate-track {
    stroke: var(--border-light);
}

/* Override default Material Design colors for all circular progress */
.mdc-circular-progress .mdc-circular-progress__determinate-circle,
.mdc-circular-progress .mdc-circular-progress__indeterminate-circle-graphic circle {
    stroke: var(--primary-color) !important;
}

.mdc-circular-progress .mdc-circular-progress__determinate-track {
    stroke: var(--border-light) !important;
}

/* Specific overrides for different contexts */
.htmx-indicator .mdc-circular-progress .mdc-circular-progress__determinate-circle,
.htmx-indicator .mdc-circular-progress .mdc-circular-progress__indeterminate-circle-graphic circle {
    stroke: var(--primary-color) !important;
}

.loading-overlay .mdc-circular-progress .mdc-circular-progress__determinate-circle,
.loading-overlay .mdc-circular-progress .mdc-circular-progress__indeterminate-circle-graphic circle {
    stroke: var(--primary-color) !important;
}

/* Page preloader uses white on primary background */
.page-preloader .mdc-circular-progress .mdc-circular-progress__determinate-circle,
.page-preloader .mdc-circular-progress .mdc-circular-progress__indeterminate-circle-graphic circle {
    stroke: var(--text-on-primary) !important;
}

/* Content area specific loading */
#content-area.htmx-request {
    min-height: 200px;
}

#content-area .htmx-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* Dialog specific loading */
#dialog.htmx-request {
    min-height: 100px;
}

/* Card specific loading */
.dashboard-card.htmx-request,
.mdc-card.htmx-request {
    position: relative;
}

/* Ensure loading indicators work with different positioning contexts */
.htmx-request {
    position: relative;
}

.htmx-request .htmx-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1001;
}

/* Special handling for fixed/absolute positioned elements */
.htmx-request.position-fixed .htmx-indicator,
.htmx-request.position-absolute .htmx-indicator {
    position: fixed;
}

/* Disable interactions during loading */
.htmx-request {
    pointer-events: none;
    user-select: none;
}

.htmx-request * {
    pointer-events: none;
}

/* Loading overlay for better visual feedback */
.htmx-request::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.6);
    z-index: 1000;
    border-radius: inherit;
}

[data-theme="dark"] .htmx-request::before {
    background: rgba(0, 0, 0, 0.6);
}

/* Dropdown Styles */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-trigger {
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.dropdown-trigger:hover {
    background: var(--surface-hover);
}

.dropdown-content {
    display: none;
    position: absolute;
    right: 0;
    top: 100%;
    background: var(--background-surface);
    min-width: 200px;
    box-shadow: 0 4px 16px var(--shadow-medium);
    border-radius: 8px;
    z-index: 1000;
    border: 1px solid var(--border-light);
    overflow: hidden;
}

.dropdown-content.show {
    display: block;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    text-decoration: none;
    color: var(--text-primary);
    transition: background-color 0.2s ease;
    font-size: 14px;
}

.dropdown-item:hover {
    background: var(--surface-hover);
    color: var(--text-primary);
}

.dropdown-item .material-icons {
    font-size: 18px;
    color: var(--text-secondary);
}

.card-content {
    padding: 20px 24px 24px;
}

/* Attendance Stats */
.attendance-stats {
    display: flex;
    justify-content: space-around;
    align-items: center;
    gap: 20px;
}

.attendance-item {
    text-align: center;
}

.attendance-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: 600;
    color: white;
    margin-bottom: 8px;
}

.attendance-circle.present {
    background: var(--success-color);
}

.attendance-circle.absent {
    background: var(--error-color);
}

.attendance-circle.late {
    background: var(--warning-color);
}

.attendance-label {
    font-size: 14px;
    color: var(--text-secondary);
}

/* Activity List */
.activity-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px;
    border-radius: 8px;
    transition: background-color 0.2s ease;
}

.activity-item:hover {
    background: var(--surface-hover);
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: var(--primary-alpha-12);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.activity-icon .material-icons {
    font-size: 20px;
    color: var(--primary-color);
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.activity-subtitle {
    font-size: 13px;
    color: var(--text-secondary);
    margin-bottom: 4px;
}

.activity-time {
    font-size: 12px;
    color: var(--text-disabled);
}

/* Quick Actions */
.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 16px;
}

.quick-action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 10px 8px;
    border-radius: 12px;
    background: var(--background-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.quick-action-item:hover {
    background: var(--primary-alpha-8);
    transform: translateY(-2px);
}

.quick-action-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 12px;
}

.quick-action-icon .material-icons {
    font-size: 24px;
    color: white;
}

.quick-action-label {
    font-size: 13px;
    font-weight: 500;
    color: var(--text-primary);
    line-height: 1.3;
}

/* Performance Table */
.performance-table {
    overflow-x: auto;
}

.performance-table .mdc-data-table__table {
    width: 100%;
    border-collapse: collapse;
}

.performance-table .mdc-data-table__header-cell,
.performance-table .mdc-data-table__cell {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid var(--border-light);
}

.performance-table .mdc-data-table__header-cell {
    font-weight: 500;
    color: var(--text-secondary);
    background: var(--background-secondary);
}

.performance-table .mdc-data-table__cell {
    color: var(--text-primary);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .dashboard-container {
        padding: 8px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .dashboard-grid {
        gap: 16px;
    }

    .dashboard-grid-2 {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .stat-card {
        padding: 20px;
    }

    .stat-card-column {
        padding: 12px;
    }

    .stat-card-header {
        margin-bottom: 8px;
        gap: 6px;
    }

    .stat-icon {
        width: 40px;
        height: 40px;
    }

    .stat-icon .material-icons {
        font-size: 20px;
    }

    .stat-grid-content {
        gap: 4px;
    }

    .stat-grid-item {
        padding: 6px 3px;
    }

    .stat-number-small {
        font-size: 16px;
    }

    .stat-label-small {
        font-size: 10px;
    }

    .stat-label-main {
        font-size: 12px;
    }

    .card-header {
        padding: 16px 20px 12px;
    }

    .card-content {
        padding: 16px 20px 20px;
    }

    .attendance-stats {
        flex-direction: column;
        gap: 16px;
    }

    .attendance-circle {
        width: 60px;
        height: 60px;
        font-size: 16px;
    }

    .quick-actions-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }

    .quick-action-item {
        padding: 16px 8px;
    }

    .quick-action-icon {
        width: 40px;
        height: 40px;
        margin-bottom: 8px;
    }

    .quick-action-icon .material-icons {
        font-size: 20px;
    }

    .quick-action-label {
        font-size: 12px;
    }
}

/* Dashboard Area */
.dashboard-area {
    width: 100%;
    min-height: 400px;
}

.error-message {
    padding: 20px;
    text-align: center;
    color: var(--error-color);
    background: rgba(244, 67, 54, 0.1);
    border-radius: 8px;
    margin: 20px;
}

/* ========================================
   TABLE ROW SELECTION STYLES
   ======================================== */

/* Enhanced checkbox states */
.mdc-data-table__row--selected {
    background-color: rgba(25, 118, 210, 0.04);
}

.mdc-data-table__row--selected:hover {
    background-color: rgba(25, 118, 210, 0.08);
}

/* Improved table row hover */
.mdc-data-table__row:not(.mdc-data-table__header-row):hover {
    background-color: rgba(0, 0, 0, 0.04);
    cursor: pointer;
}

.mdc-data-table__row--selected:hover {
    background-color: rgba(25, 118, 210, 0.08) !important;
}

/* Search field enhancements */
.search-container {
    display: flex;
    align-items: center;
    gap: 8px;
}

.search-label {
    font-size: 14px;
    color: #5f6368;
    white-space: nowrap;
}

.mdc-search-input {
    min-width: 250px;
}

/* Table controls layout */
.table-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    gap: 16px;
}

.per-page-container {
    display: flex;
    align-items: center;
    gap: 8px;
}

.per-page-label {
    font-size: 14px;
    color: #5f6368;
    white-space: nowrap;
}

/* Table action buttons */
.table-actions {
    display: flex;
    align-items: center;
    gap: 4px;
}

.action-btn {
    background: transparent;
    border: none;
    padding: 6px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--text-secondary);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn:hover {
    background-color: var(--surface-hover);
    color: var(--text-primary);
}

.action-btn.edit-btn:hover {
    background-color: var(--primary-alpha-8);
    color: var(--primary-color);
}

.action-btn.payment-btn:hover {
    background-color: rgba(76, 175, 80, 0.08);
    color: #4caf50;
}

.action-btn.delete-btn:hover {
    background-color: rgba(244, 67, 54, 0.08);
    color: #f44336;
}

.action-btn .material-icons {
    font-size: 18px;
}

/* Student photo in table */
.student-photo {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border: 2px solid var(--border-light);
}

/* Status badges */
.status-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-active {
    background-color: #e8f5e8;
    color: #2e7d32;
}

.status-inactive {
    background-color: #ffebee;
    color: #c62828;
}

/* Amount styling */
.numeric-cell {
    text-align: right;
    font-family: 'Roboto Mono', monospace;
}

.amount-positive {
    color: #2e7d32;
    font-weight: 500;
}

.amount-negative {
    color: #c62828;
    font-weight: 500;
}

.amount-warning {
    color: #f57c00;
    font-weight: 500;
}

/* Utilities */
.hidden-on-small-screens {
    display: block;
}

@media (max-width: 768px) {
    .hidden-on-small-screens {
        display: none !important;
    }
}

/* Sidebar Drill-down Navigation */
.sidebar-submenu-detail {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--sidebar-bg);
    z-index: 10;
    display: flex;
    flex-direction: column;
}

.submenu-detail-header {
    display: flex;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
    background: var(--sidebar-bg);
    position: sticky;
    top: 0;
    z-index: 11;
}

.back-btn {
    background: none;
    border: none;
    color: var(--text-color);
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.back-btn:hover {
    background: var(--hover-bg);
}

.back-btn .material-icons {
    font-size: 24px;
}

.submenu-detail-title {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
    color: var(--text-color);
}

.submenu-detail-content {
    flex: 1;
    overflow-y: auto;
    padding: 8px 0;
}

.submenu-detail-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    color: var(--text-color);
    text-decoration: none;
    transition: background-color 0.2s;
    border-radius: 0;
    margin: 0 8px;
    border-radius: 8px;
}

.submenu-detail-item:hover {
    background: var(--hover-bg);
    color: var(--text-color);
    text-decoration: none;
}

.submenu-detail-item .material-icons {
    margin-right: 12px;
    font-size: 20px;
    color: var(--primary-color);
}

.submenu-detail-item span:last-child {
    font-size: 14px;
    font-weight: 400;
}

/* Update expand icons for drill-down */
.sidebar-item-with-submenu .expand-icon {
    margin-left: auto;
    transition: transform 0.2s;
}

/* Hide original submenus when using drill-down */
.sidebar .submenu {
    display: none;
}