from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Permission
from school import models
import json

User = get_user_model()


class StudentsListAPITest(TestCase):
    def setUp(self):
        # Create a test user with permissions
        self.user = User.objects.create_user(
            phone='1234567890',
            password='testpass123'
        )
        
        # Add required permission
        permission = Permission.objects.get(codename='view_payment')
        self.user.user_permissions.add(permission)
        
        # Create a test school
        self.school = models.School.objects.create(
            name='Test School',
            education='FR'
        )
        self.user.school = self.school
        self.user.save()
        
        # Create test data
        self.student = models.Student.objects.create(
            first_name='<PERSON>',
            last_name='<PERSON>e',
            birth_date='2010-01-01',
            gender='M',
            school=self.school
        )
        
        self.level = models.Level.objects.create(
            number=6,
            school=self.school
        )
        
        self.enrollment = models.Enrollment.objects.create(
            student=self.student,
            level_fr=self.level,
            school=self.school,
            year=models.SchoolYear.objects.get_or_create(
                year='2024-2025',
                school=self.school
            )[0]
        )
        
        self.client = Client()
        self.client.login(phone='1234567890', password='testpass123')
        
    def test_html_response(self):
        """Test that the view returns HTML by default"""
        url = reverse('school:students')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Liste des élèves')
        self.assertContains(response, 'x-data="studentsApp()"')
        
    def test_json_response_with_format_param(self):
        """Test JSON response when format=json parameter is provided"""
        url = reverse('school:students')
        response = self.client.get(url, {'format': 'json'})
        
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        
        data = json.loads(response.content)
        
        # Check response structure
        self.assertIn('students', data)
        self.assertIn('pagination', data)
        self.assertIn('filters', data)
        
        # Check pagination structure
        pagination = data['pagination']
        self.assertIn('current_page', pagination)
        self.assertIn('total_pages', pagination)
        self.assertIn('total_count', pagination)
        self.assertIn('has_next', pagination)
        self.assertIn('has_previous', pagination)
        self.assertIn('per_page', pagination)
        
        # Check filters structure
        filters = data['filters']
        self.assertIn('search', filters)
        self.assertIn('filter_by', filters)
        self.assertIn('sort', filters)
        
    def test_json_response_with_accept_header(self):
        """Test JSON response when Accept header is application/json"""
        url = reverse('school:students')
        response = self.client.get(url, HTTP_ACCEPT='application/json')
        
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        
        data = json.loads(response.content)
        self.assertIn('students', data)
        
    def test_student_data_structure(self):
        """Test that student data has the correct structure"""
        url = reverse('school:students')
        response = self.client.get(url, {'format': 'json'})
        
        data = json.loads(response.content)
        students = data['students']
        
        if students:  # If there are students in the response
            student = students[0]
            
            # Check required fields
            required_fields = [
                'id', 'student_id', 'name', 'name_ar', 'photo',
                'level_fr', 'level_ar', 'gender', 'status', 'active',
                'debt', 'amount', 'paid', 'remaining', 'birth_date',
                'birth_place', 'nationality'
            ]
            
            for field in required_fields:
                self.assertIn(field, student, f"Field '{field}' missing from student data")
                
    def test_pagination_functionality(self):
        """Test pagination parameters"""
        url = reverse('school:students')
        
        # Test per_page parameter
        response = self.client.get(url, {'format': 'json', 'per_page': 5})
        data = json.loads(response.content)
        self.assertEqual(data['pagination']['per_page'], 5)
        
        # Test page parameter
        response = self.client.get(url, {'format': 'json', 'page': 1})
        data = json.loads(response.content)
        self.assertEqual(data['pagination']['current_page'], 1)
        
    def test_search_functionality(self):
        """Test search parameter"""
        url = reverse('school:students')
        response = self.client.get(url, {
            'format': 'json', 
            'search': 'John'
        })
        
        data = json.loads(response.content)
        self.assertEqual(data['filters']['search'], 'John')
        
    def test_filter_functionality(self):
        """Test filter_by parameter"""
        url = reverse('school:students')
        response = self.client.get(url, {
            'format': 'json', 
            'filter_by': 'paid'
        })
        
        data = json.loads(response.content)
        self.assertEqual(data['filters']['filter_by'], 'paid')
