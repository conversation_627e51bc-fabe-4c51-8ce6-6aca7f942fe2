{% extends 'material/base.html' %}
{% load static %}
{% load humanize %}

{% block title %}Liste des élèves{% endblock %}

{% block content %}
<div x-data="studentsApp()" x-init="$nextTick(() => { loadStudents(); window.addEventListener('resize', () => { isMobile = window.innerWidth <= 768; }); })"
    <!-- Header -->
    <div class="content-header" id="content-header">
        <span class="material-icons back-btn" onclick="history.back()" title="Retour">arrow_back</span>
        <h2 class="page-title">
            <span>Liste des élèves</span>
            <span class="item-count" x-text="`(${pagination.total_count || 0} élèves)`"></span>
        </h2>
        <div class="actions">
            <button class="mdc-button mdc-button--raised add-btn" id="add-student-btn">
                <span class="mdc-button__ripple"></span>
                <span class="material-icons" style="pointer-events: none;">add</span>
                <span class="mdc-button__label">Ajouter un élève</span>
            </button>
            <span class="material-icons search-icon-mobile" title="Rechercher" id="mobile-search-btn">search</span>
            <span class="material-icons" title="Filtrer" id="filter-btn">filter_list</span>
        </div>
    </div>

    <!-- Content Area -->
    <div class="content-area" id="content-area">
        <!-- Quick Filter Chips -->
        <div class="quick-filter-chips">
            <button class="quick-filter-chip" 
                    :class="{ 'active': filters.filter_by === 'all' }"
                    @click="setFilter('all')">
                <span class="material-icons">group</span>
                <span>Tous</span>
            </button>
            <button class="quick-filter-chip" 
                    :class="{ 'active': filters.filter_by === 'paid' }"
                    @click="setFilter('paid')">
                <span class="material-icons">paid</span>
                <span>Payé</span>
            </button>
            <button class="quick-filter-chip" 
                    :class="{ 'active': filters.filter_by === 'unpaid' }"
                    @click="setFilter('unpaid')">
                <span class="material-icons">money_off</span>
                <span>Non payé</span>
            </button>
            <button class="quick-filter-chip" 
                    :class="{ 'active': filters.filter_by === 'full_paid' }"
                    @click="setFilter('full_paid')">
                <span class="material-icons">check_circle</span>
                <span>Soldé</span>
            </button>
            <button class="quick-filter-chip" 
                    :class="{ 'active': filters.filter_by === 'today' }"
                    @click="setFilter('today')">
                <span class="material-icons">today</span>
                <span>Aujourd'hui</span>
            </button>
        </div>

        <!-- Table Controls -->
        <div class="table-controls">
            <div class="search-container">
                <span class="search-label">Rechercher:</span>
                <div class="mdc-text-field mdc-text-field--outlined mdc-text-field--with-leading-icon mdc-small-input mdc-search-input">
                    <span class="mdc-text-field__icon mdc-text-field__icon--leading material-icons">search</span>
                    <input type="text" class="mdc-text-field__input" id="table-search" 
                        placeholder="Rechercher des élèves..."
                        x-model="filters.search"
                        @input.debounce.500ms="loadStudents()">
                    <div class="mdc-notched-outline">
                        <div class="mdc-notched-outline__leading"></div>
                        <div class="mdc-notched-outline__notch"></div>
                        <div class="mdc-notched-outline__trailing"></div>
                    </div>
                </div>
            </div>
            <div class="per-page-container">
                <span class="per-page-label">Lignes par page:</span>
                <div class="mdc-select mdc-select--outlined mdc-small-input" id="per-page-select">
                    <div class="mdc-select__anchor" role="button" aria-haspopup="listbox" aria-expanded="false">
                        <span class="mdc-select__selected-text-container">
                            <span class="mdc-select__selected-text" x-text="pagination.per_page"></span>
                        </span>
                        <span class="mdc-select__dropdown-icon">
                            <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5" focusable="false">
                                <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                            </svg>
                        </span>
                        <div class="mdc-notched-outline">
                            <div class="mdc-notched-outline__leading"></div>
                            <div class="mdc-notched-outline__trailing"></div>
                        </div>
                    </div>
                    <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                        <ul class="mdc-deprecated-list" role="listbox">
                            <li class="mdc-deprecated-list-item" data-value="5" role="option" @click="changePerPage(5)">
                                <span class="mdc-deprecated-list-item__ripple"></span>
                                <span class="mdc-deprecated-list-item__text">5</span>
                            </li>
                            <li class="mdc-deprecated-list-item" data-value="10" role="option" @click="changePerPage(10)">
                                <span class="mdc-deprecated-list-item__ripple"></span>
                                <span class="mdc-deprecated-list-item__text">10</span>
                            </li>
                            <li class="mdc-deprecated-list-item" data-value="25" role="option" @click="changePerPage(25)">
                                <span class="mdc-deprecated-list-item__ripple"></span>
                                <span class="mdc-deprecated-list-item__text">25</span>
                            </li>
                            <li class="mdc-deprecated-list-item" data-value="50" role="option" @click="changePerPage(50)">
                                <span class="mdc-deprecated-list-item__ripple"></span>
                                <span class="mdc-deprecated-list-item__text">50</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div class="loading-overlay" x-show="loading" x-transition>
            <div class="loading-content">
                <div class="mdc-circular-progress mdc-circular-progress--indeterminate" role="progressbar">
                    <div class="mdc-circular-progress__determinate-container">
                        <svg class="mdc-circular-progress__determinate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                            <circle class="mdc-circular-progress__determinate-track" cx="24" cy="24" r="18" stroke-width="4"/>
                            <circle class="mdc-circular-progress__determinate-circle" cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="113.097" stroke-width="4"/>
                        </svg>
                    </div>
                    <div class="mdc-circular-progress__indeterminate-container">
                        <div class="mdc-circular-progress__spinner-layer">
                            <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left">
                                <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="56.549" stroke-width="4"/>
                                </svg>
                            </div>
                            <div class="mdc-circular-progress__gap-patch">
                                <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="56.549" stroke-width="3.2"/>
                                </svg>
                            </div>
                            <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right">
                                <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="56.549" stroke-width="4"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="loading-text">Chargement des élèves...</div>
            </div>
        </div>

        <!-- Desktop Table View -->
        <div class="data-table-container" x-show="!isMobile">
            <div class="mdc-data-table">
                <div class="mdc-data-table__table-container">
                    <table class="mdc-data-table__table" aria-label="Liste des élèves">
                        <thead>
                            <tr class="mdc-data-table__header-row">
                                <th class="mdc-data-table__header-cell mdc-data-table__header-cell--checkbox" role="columnheader" scope="col">
                                    <div class="mdc-checkbox mdc-data-table__header-row-checkbox">
                                        <input type="checkbox" class="mdc-checkbox__native-control" id="select-all-checkbox" aria-label="Sélectionner toutes les lignes">
                                        <div class="mdc-checkbox__background">
                                            <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                                <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                            </svg>
                                            <div class="mdc-checkbox__mixedmark"></div>
                                        </div>
                                        <div class="mdc-checkbox__ripple"></div>
                                    </div>
                                </th>
                                <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Photo</th>
                                <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Nom et Prénoms</th>
                                <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Matricule</th>
                                <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Classe</th>
                                <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Sexe</th>
                                <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Statut</th>
                                <th class="mdc-data-table__header-cell numeric-column" role="columnheader" scope="col">Arriéré</th>
                                <th class="mdc-data-table__header-cell numeric-column" role="columnheader" scope="col">À payer</th>
                                <th class="mdc-data-table__header-cell numeric-column" role="columnheader" scope="col">Payé</th>
                                <th class="mdc-data-table__header-cell numeric-column" role="columnheader" scope="col">Reste</th>
                                <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="mdc-data-table__content" id="table-body">
                            <template x-for="student in students" :key="student.id">
                                <tr class="mdc-data-table__row table-row" :data-student-id="student.id">
                                    <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                                        <div class="mdc-checkbox mdc-data-table__row-checkbox">
                                            <input type="checkbox" class="mdc-checkbox__native-control row-checkbox" aria-label="Sélectionner la ligne">
                                            <div class="mdc-checkbox__background">
                                                <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                                    <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                                </svg>
                                                <div class="mdc-checkbox__mixedmark"></div>
                                            </div>
                                            <div class="mdc-checkbox__ripple"></div>
                                        </div>
                                    </td>
                                    <td class="mdc-data-table__cell">
                                        <div class="student-photo" :style="`background-image: url('${student.photo}')`"></div>
                                    </td>
                                    <th class="mdc-data-table__cell" scope="row">
                                        <div x-text="student.name"></div>
                                        <div class="text-muted small" x-text="student.name_ar" x-show="student.name_ar"></div>
                                    </th>
                                    <td class="mdc-data-table__cell" x-text="student.student_id || '-'"></td>
                                    <td class="mdc-data-table__cell" x-text="student.level_fr || '-'"></td>
                                    <td class="mdc-data-table__cell" x-text="student.gender"></td>
                                    <td class="mdc-data-table__cell">
                                        <span class="status-badge" :class="student.active ? 'status-active' : 'status-inactive'" x-text="student.status"></span>
                                    </td>
                                    <td class="mdc-data-table__cell numeric-cell amount-negative" x-text="formatAmount(student.debt)"></td>
                                    <td class="mdc-data-table__cell numeric-cell" x-text="formatAmount(student.amount)"></td>
                                    <td class="mdc-data-table__cell numeric-cell" :class="student.paid > 0 ? 'amount-positive' : ''" x-text="formatAmount(student.paid)"></td>
                                    <td class="mdc-data-table__cell numeric-cell" 
                                        :class="student.remaining == 0 && student.amount > 0 ? 'amount-positive' : (student.remaining > 0 ? 'amount-warning' : '')"
                                        x-text="student.remaining == 0 && student.amount > 0 ? 'Soldé' : formatAmount(student.remaining)"></td>
                                    <td class="mdc-data-table__cell">
                                        <div class="table-actions">
                                            <button class="action-btn edit-btn" title="Modifier l'élève">
                                                <span class="material-icons">edit</span>
                                            </button>
                                            <button class="action-btn payment-btn" title="Ajouter/Modifier un paiement">
                                                <span class="material-icons">payments</span>
                                            </button>
                                            <button class="action-btn delete-btn" title="Supprimer l'élève">
                                                <span class="material-icons">delete</span>
                                            </button>
                                            <button class="action-btn more-btn" title="Plus d'options">
                                                <span class="material-icons">more_vert</span>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </template>
                            <!-- Empty State -->
                            <tr class="mdc-data-table__row" x-show="students.length === 0 && !loading">
                                <td class="mdc-data-table__cell" colspan="12" style="text-align: center; padding: 48px;">
                                    <div style="color: #757575;">
                                        <span class="material-icons" style="font-size: 48px; margin-bottom: 16px; display: block;">search_off</span>
                                        Aucun élève trouvé
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Mobile List View -->
        <div class="students-list" x-show="isMobile">
            <template x-for="student in students" :key="student.id">
                <div class="student-item" :data-student-id="student.id">
                    <div class="student-photo" :style="`background-image: url('${student.photo}')`"></div>
                    <div class="student-info">
                        <div class="student-header">
                            <div class="student-name" x-text="student.name"></div>
                            <div class="student-id" x-text="student.student_id"></div>
                        </div>
                        <div class="student-name-ar" x-text="student.name_ar" x-show="student.name_ar"></div>
                        <div class="student-birth-date" x-text="`${student.birth_place} • ${student.gender}`"></div>
                        <div class="student-grades">
                            <span class="grade-fr" x-text="student.level_fr"></span>
                            <span class="grade-separator" x-show="student.level_ar">•</span>
                            <span class="grade-ar" x-text="student.level_ar" x-show="student.level_ar"></span>
                        </div>
                        <div class="student-payment-info">
                            <span class="payment-amount" x-text="`Payé: ${formatAmount(student.paid)}`"></span>
                            <span class="remaining-amount" x-text="`Reste: ${student.remaining == 0 && student.amount > 0 ? 'Soldé' : formatAmount(student.remaining)}`"></span>
                        </div>
                    </div>
                    <div class="student-actions">
                        <button class="edit-btn" title="Modifier l'élève">
                            <span class="material-icons">edit</span>
                        </button>
                        <button class="payment-btn" title="Paiement">
                            <span class="material-icons">payments</span>
                        </button>
                    </div>
                </div>
            </template>
            
            <!-- Mobile Empty State -->
            <div class="empty-state" x-show="students.length === 0 && !loading">
                <span class="material-icons">search_off</span>
                <div class="empty-text">Aucun élève trouvé</div>
            </div>
        </div>

        <!-- Pagination -->
        <div class="pagination-container" x-show="pagination.total_pages > 1">
            <div class="pagination-info">
                <span x-text="`Page ${pagination.current_page} sur ${pagination.total_pages}`"></span>
                <span x-text="`(${pagination.total_count} élèves au total)`"></span>
            </div>
            <div class="pagination-controls">
                <button class="pagination-btn" 
                        :disabled="!pagination.has_previous" 
                        @click="changePage(pagination.current_page - 1)">
                    <span class="material-icons">chevron_left</span>
                </button>
                <button class="pagination-btn" 
                        :disabled="!pagination.has_next" 
                        @click="changePage(pagination.current_page + 1)">
                    <span class="material-icons">chevron_right</span>
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function studentsApp() {
    return {
        // Data
        students: [],
        loading: false,
        isMobile: window.innerWidth <= 768,

        // Pagination
        pagination: {
            current_page: 1,
            total_pages: 1,
            total_count: 0,
            has_next: false,
            has_previous: false,
            per_page: 10
        },

        // Filters
        filters: {
            search: '',
            filter_by: 'all',
            sort: ''
        },


        // Load students data from API
        async loadStudents() {
            this.loading = true;

            try {
                const params = new URLSearchParams({
                    format: 'json',
                    page: this.pagination.current_page,
                    per_page: this.pagination.per_page,
                    search: this.filters.search,
                    filter_by: this.filters.filter_by,
                    sort: this.filters.sort
                });

                const response = await fetch(`{{ request.path }}?${params}`);
                const data = await response.json();

                this.students = data.students;
                this.pagination = data.pagination;
                this.filters = { ...this.filters, ...data.filters };

            } catch (error) {
                console.error('Error loading students:', error);
                // Show error message to user
                this.showError('Erreur lors du chargement des élèves');
            } finally {
                this.loading = false;
            }
        },

        // Set filter and reload
        setFilter(filterValue) {
            this.filters.filter_by = filterValue;
            this.pagination.current_page = 1; // Reset to first page
            this.loadStudents();
        },

        // Change page
        changePage(page) {
            if (page >= 1 && page <= this.pagination.total_pages) {
                this.pagination.current_page = page;
                this.loadStudents();
            }
        },

        // Change per page
        changePerPage(perPage) {
            this.pagination.per_page = perPage;
            this.pagination.current_page = 1; // Reset to first page
            this.loadStudents();
        },

        // Format amount for display
        formatAmount(amount) {
            if (!amount || amount === 0) return '-';
            return new Intl.NumberFormat('fr-FR').format(amount);
        },

        // Show error message
        showError(message) {
            // You can implement a toast notification here
            alert(message);
        }
    }
}
</script>
{% endblock %}
